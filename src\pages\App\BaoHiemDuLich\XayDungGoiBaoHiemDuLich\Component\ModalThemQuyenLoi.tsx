import {CheckOutlined, DownOutlined} from "@ant-design/icons";
import {<PERSON><PERSON>, HeaderModal} from "@src/components";
import {Flex, Input, Modal, Tree, TreeDataNode, TreeProps} from "antd";
import {cloneDeep, isEqual} from "lodash";
import {CheckInfo} from "rc-tree/lib/Tree";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useState} from "react";
import {IModalThemQuyenLoiRef, ModalThemQuyenLoiProps} from "./Constant";
import {useXayDungGoiBaoHiemContext} from "../index.context";

const {Search} = Input;

const getParentKey = (key: React.Key, tree: TreeDataNode[]): React.Key => {
  let parentKey: React.Key;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some(item => item.key === key)) {
        parent<PERSON>ey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey!;
};

//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
const ModalThemQuyenLoiComponent = forwardRef<IModalThemQuyenLoiRef, ModalThemQuyenLoiProps>(
  ({quyenLoiDangXem, listQuyenLoiRoot, listQuyenLoi, checkStrictly, setIsOpenQuyenLoiTruLui, onPressLuuQuyenLoiTruLui, onPressThemQuyenLoi}: ModalThemQuyenLoiProps, ref) => {
    useImperativeHandle(ref, () => ({
      close: () => {},
    }));

    const {windowHeight} = useXayDungGoiBaoHiemContext();

    const [arrQuyenLoiDisable, setArrQuyenLoiDisable] = useState<string[]>([]); //list quyền lợi cần disable trong trường hợp chọn quyền lợi bổ sung

    const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]); //xử lý lưu list quyền lợi bổ sung

    const [searchValue, setSearchValue] = useState<string>("");
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState(true);

    //so sánh height số lượng node trong tree có bé hơn height modal không
    const heightNodeTreeBeHonTreeHeight = useMemo(() => {
      //(windowHeight / 100) * 60 : modal height 60vh
      //32 : header
      //44 : footer
      //24 : node height
      const modalTree = (windowHeight / 100) * 60 - 32 - 44;
      if (listQuyenLoiRoot.length * 24 < modalTree) return true;
      return false;
    }, [windowHeight, listQuyenLoiRoot]);

    useEffect(() => {
      if (quyenLoiDangXem) {
        getListMaQuyenLoiChaCon();
      }
    }, [quyenLoiDangXem]);

    //xử lý lấy ra arrMa quyền lời cha/con của quyền lợi trừ lùi
    const getListMaQuyenLoiChaCon = () => {
      try {
        const arrMaQuyenLoiDisableCheck = [];
        const listNodeTmp = listQuyenLoiRoot;
        const indexBatDauLoaiBo = listNodeTmp.findIndex(item => item.ma === quyenLoiDangXem?.ma_qloi);
        let indexKetThucLoaiBo = -1;
        for (let index = indexBatDauLoaiBo + 1; index < listNodeTmp.length; index++) {
          //nếu thằng tiếp theo có cap > thì lấy vị trí kết thúc = vị trí bắt đầu
          if (listNodeTmp[index].cap < (quyenLoiDangXem?.cap || -1)) {
            indexKetThucLoaiBo = indexBatDauLoaiBo;
            break;
          }
          if (listNodeTmp[index].cap === quyenLoiDangXem?.cap) {
            indexKetThucLoaiBo = index - 1;
            break;
          }
        }
        if (indexKetThucLoaiBo == -1) indexKetThucLoaiBo = listNodeTmp.length - 1; //trường hợp thằng cuối cùng -> sẽ k tìm dc thằng cùng cấp tương ứng
        if (indexBatDauLoaiBo >= 0) {
          for (let index = indexBatDauLoaiBo; index <= indexKetThucLoaiBo; index++) {
            arrMaQuyenLoiDisableCheck?.push(listNodeTmp[index].ma);
          }
        }
        setArrQuyenLoiDisable(arrMaQuyenLoiDisableCheck);
      } catch (error) {
        console.log("getListMaQuyenLoiChaCon", error);
      }
    };

    useEffect(() => {
      //trường hợp chọn quyền lợi trừ lùi
      if (listQuyenLoi.length > 0 && quyenLoiDangXem) {
        setCheckedKeys(quyenLoiDangXem.ma_qloi_tru_lui ? quyenLoiDangXem.ma_qloi_tru_lui.split(";") : []);
      }
      // trường hợp thêm quyền lợi vào table
      else if (listQuyenLoi.length > 0) {
        setCheckedKeys(listQuyenLoi.map(item => item.ma_qloi || ""));
      }
    }, [quyenLoiDangXem, listQuyenLoi]);

    const initTreeQuyenLoi = () => {
      try {
        let listNodeTmp: Array<any> = cloneDeep(listQuyenLoiRoot);
        listNodeTmp = listNodeTmp.map((item, index) => {
          // item.title = index + 1 + " - " + item.cap + " - " + item.ma + " - " + item.ma_ct + " - " + item.ten;
          item.title = item.ma + " - " + item.ten;
          item.key = item.ma;
          return item;
        });
        const listNodeCap1 = listNodeTmp.filter(item => item.cap === 1);
        for (let i = 0; i < listNodeCap1.length; i++) {
          const listNodeConLai = listNodeTmp.filter(item => item.cap !== 1);

          listNodeCap1[i].children = deQuyLayNodeCon(listNodeCap1[i], listNodeConLai);
          if (quyenLoiDangXem) {
            if (listNodeCap1[i].children.length === 0) listNodeCap1[i].disableCheckbox = false;
            else listNodeCap1[i].disableCheckbox = true;
          }
        }
        return listNodeCap1;
      } catch (error) {
        console.log("initTreeQuyenLoi", error);
      }
    };

    const deQuyLayNodeCon = (nodeCha: any, listNodeTimKiem: any) => {
      try {
        const listNodeConFilter = listNodeTimKiem.filter((item: any) => item.ma_ct === nodeCha.ma);
        const listNodeConLai = listNodeTimKiem.filter((item: any) => item.ma_ct !== nodeCha.ma);
        if (listNodeConFilter.length === 0) return [];
        if (listNodeConFilter.length > 0 && listNodeConLai.length === 0) return listNodeConLai;
        else {
          for (let i = 0; i < listNodeConFilter.length; i++) {
            listNodeConFilter[i].children = deQuyLayNodeCon(listNodeConFilter[i], listNodeConLai);
            if (quyenLoiDangXem) {
              if (listNodeConFilter[i].children.length === 0) listNodeConFilter[i].disableCheckbox = false;
              else listNodeConFilter[i].disableCheckbox = true;
            }
          }
          return listNodeConFilter;
        }
      } catch (error) {
        console.log("deQuyLayNodeCon error", error);
      }
    };

    const closeModal = useCallback(() => {
      setIsOpenQuyenLoiTruLui(false);
      setCheckedKeys([]); // reset trạng thái đã check
    }, [setIsOpenQuyenLoiTruLui]);

    //XỬ LÝ KHI CLICK VÀO TITLE - ĐANG XỬ LÝ KHÔNG ĐÚNG LẮM ?
    const onSelect = (selectedKeys: React.Key[], info: any) => {
      setCheckedKeys(prev => {
        const isChecked = prev.includes(info.node.key);
        return isChecked ? prev.filter(k => k !== info.node.key) : [...prev, info.node.key];
      });
    };

    //XỬ LÝ KHI CHECKBOX ĐƯỢC CHECKED
    const onCheck: TreeProps["onCheck"] = (checkedKeys: any, info: CheckInfo<any>) => {
      try {
        //BIẾN checkedKeys SẼ KHÁC NHAU KHI checkStrictly THAY ĐỔI GIÁ TRỊ TRUE / FALSE
        let listCheckKeys = checkedKeys.checked || checkedKeys;
        //trường hợp là chọn quyền lợi trừ lùi -> xử lý như thường
        if (quyenLoiDangXem) {
          setCheckedKeys(checkedKeys);
        }
        // trường hợp bổ sung quyền lợi vào gói -> xử lý tay các checkedKeys
        else {
          const listChaBoSung = [];

          //xử lý khi node được check true
          if (info.checked) {
            const viTriClick = listQuyenLoiRoot.findIndex(item => item.ma === info.node.ma); //tìm ra vị trí click
            if (viTriClick >= 0) {
              let capNodeClick = info.node.cap; //lấy ra cấp
              /*
               xử lý khi click true vào con, thằng cha sẽ được click true theo
             logic là từ mã thằng click, duyệt theo mảng listQuyenLoiRoot(mảng này trả từ server dạng tree nhưng là phẳng ra),
             sau đó dựa vào capNodeClick để truy ngược(duyệt từ dưới lên trên) về các cấp cha của nó
              */
              for (let i = viTriClick - 1; i >= 0; i--) {
                if (capNodeClick < 0) break;
                if (listQuyenLoiRoot[i].cap === capNodeClick - 1) {
                  listChaBoSung.push(listQuyenLoiRoot[i].ma);
                  capNodeClick -= 1;
                }
              }
            }
            listCheckKeys = listCheckKeys.concat(listChaBoSung);
            listCheckKeys = listCheckKeys.filter((item: any, index: number) => listCheckKeys.indexOf(item) === index); //loại bỏ các key bị trùng
          } else {
            //xử lý case mặc dù đã info.checked = false rồi nhưng list checkedKeys.checked bị lỗi vẫn còn key đấy
            const viTriClick = listCheckKeys.findIndex((item: any) => item === info.node.ma); //tìm ra vị trí click
            if (viTriClick >= 0) {
              listCheckKeys.splice(viTriClick, 1); //nếu bỏ check mà vị trí vẫn còn trong mảng -> lỗi -> gọi splice bỏ ra khỏi mảng
            }
            //xử lý khi bỏ check node cha cap=1 -> sẽ bỏ check tất cả các node con
            const arrMaQuyenLoiDisableCheck: any = [];
            const viTriBatDau = listQuyenLoiRoot.findIndex(item => item.ma === info.node.ma);
            let viTriKetThucBoCheck = -1;
            for (let index = viTriBatDau + 1; index < listQuyenLoiRoot.length; index++) {
              //nếu thằng tiếp theo có cap > thì lấy vị trí kết thúc = vị trí bắt đầu
              if (listQuyenLoiRoot[index].cap < info.node.cap) {
                viTriKetThucBoCheck = viTriBatDau;
                break;
              }
              if (listQuyenLoiRoot[index].cap === info.node.cap) {
                viTriKetThucBoCheck = index - 1;
                break;
              }
            }
            if (viTriKetThucBoCheck == -1) viTriKetThucBoCheck = listQuyenLoiRoot.length - 1; //trường hợp thằng cuối cùng -> sẽ k tìm dc thằng cùng cấp tương ứng
            if (viTriBatDau >= 0) {
              for (let index = viTriBatDau; index <= viTriKetThucBoCheck; index++) {
                arrMaQuyenLoiDisableCheck.push(listQuyenLoiRoot[index].ma);
              }
            }
            listCheckKeys = listCheckKeys.filter((item: any) => !arrMaQuyenLoiDisableCheck.includes(item));
          }

          setCheckedKeys([...listCheckKeys]);
        }
      } catch (error) {
        console.log("onCheck error", error);
      }
    };

    const onConfirm = useCallback(() => {
      //nếu có quyenLoiDangXem -> đang bổ sung quyền lợi
      if (quyenLoiDangXem) {
        onPressLuuQuyenLoiTruLui(checkedKeys as string[], quyenLoiDangXem?.ma_qloi);
        setIsOpenQuyenLoiTruLui(false);
      } else {
        onPressThemQuyenLoi(checkedKeys as string[]);
        setIsOpenQuyenLoiTruLui(false);
      }
    }, [checkedKeys, quyenLoiDangXem, onPressLuuQuyenLoiTruLui, setIsOpenQuyenLoiTruLui]);

    // const onExpand = (newExpandedKeys: React.Key[]) => {
    //   setExpandedKeys(newExpandedKeys);
    //   setAutoExpandParent(false);
    // };

    const onChangeTextSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
      const {value} = e.target;
      // const newExpandedKeys = dataList
      //   .map(item => {
      //     if (item.title.indexOf(value) > -1) {
      //       return getParentKey(item.key, []);
      //     }
      //     return null;
      //   })
      //   .filter((item, i, self): item is React.Key => !!(item && self.indexOf(item) === i));
      // setExpandedKeys(newExpandedKeys);
      setSearchValue(value);
      // setAutoExpandParent(true);
    };

    //hàm này sẽ thực hiện xử lý searchValue tree
    const treeData = useMemo(() => {
      const loop = (data: Array<TreeDataNode & {cap: number; children: []}>): Array<TreeDataNode & {cap: number}> =>
        data.map(item => {
          const strTitle = item.title as string;
          let className = "";
          if (item.cap === 1) className = "font-bold";
          else if (item.cap === 3) className = "italic";
          const index = strTitle.toLowerCase().indexOf(searchValue.toLowerCase());
          const beforeStr = strTitle.substring(0, index); //đoạn text trước
          const centerStr = strTitle.slice(index, index + searchValue.length); //đoạn text giống đoạn text search
          const afterStr = strTitle.slice(index + searchValue.length); //đoạn text sau
          const title =
            index > -1 ? (
              <span key={item.key} className={className}>
                {beforeStr}
                <span style={{color: "#f50"}}>{centerStr}</span>
                {afterStr}
              </span>
            ) : (
              <span key={item.key} className={className}>
                {strTitle}
              </span>
            );

          //xử lý trường hợp chọn quyền lời trừ lùi -> sẽ có arrQuyenLoiDisable
          let disableCheckbox = false;
          if (item.disableCheckbox) disableCheckbox = true;
          else disableCheckbox = arrQuyenLoiDisable.includes(item.key as string) ? true : false;
          //end xử lý trường hợp chọn quyền lời trừ lùi -> sẽ có arrQuyenLoiDisable

          //nếu vẫn còn children thì gọi đệ quy tiếp
          if (item.children) {
            return {...item, title, children: loop(item.children), disableCheckbox};
          }
          //nếu k có children thì kết thúc đệ quy
          return {
            ...item,
            title,
            disableCheckbox,
          };
        });
      const result = loop(initTreeQuyenLoi() as Array<TreeDataNode & {cap: number; children: []}>);
      return result;
    }, [searchValue, arrQuyenLoiDisable]);

    // RENDER
    //FOOTER
    const renderFooter = () => {
      // const disableSubmit = checkedKeys.length === 0 ? true : false;
      const disableSubmit = false;
      return (
        <Button type="primary" icon={<CheckOutlined />} disabled={disableSubmit} onClick={onConfirm}>
          Lưu
        </Button>
      );
    };

    return (
      <Flex vertical gap="middle" align="center">
        <Modal
          className="modal-them-quyen-loi"
          title={<HeaderModal title={quyenLoiDangXem ? "Chọn quyền lợi trừ lùi" : "Thêm quyền lợi vào gói"} />}
          centered
          open={true}
          maskClosable={false}
          // onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={"50vw"}
          styles={{
            body: {
              height: "60vh",
            },
          }}
          footer={renderFooter}>
          <Search placeholder="Tìm mã, tên quyền lợi" onChange={onChangeTextSearch} className="mb-1" />
          <div className={heightNodeTreeBeHonTreeHeight ? "" : "tree-quyen-loi"}>
            <Tree
              treeData={treeData}
              checkable // add checkbox trước tree node
              // onSelect={onSelect}
              onCheck={onCheck} // callback khi checkbox được check
              checkedKeys={checkedKeys} //list các checkedkey
              // onExpand={onExpand}
              // expandedKeys={expandedKeys}
              // autoExpandParent={autoExpandParent}
              defaultExpandAll //mặc định expandall
              // height={windowHeight - (windowHeight <= 754 ? 350 : 410)} //height của tree
              showLine //showline dọc
              switcherIcon={<DownOutlined />} //icon toggle
              // defaultExpandedKeys={defaultExpandKeys}
              blockNode // 1 node sẽ chiếm full width
              checkStrictly={checkStrictly} //node cha và con sẽ không được liên kết, khi click vào cha sẽ không click vào con và ngược lại
              // checkStrictly={false} //node cha và con sẽ không được liên kết, khi click vào cha sẽ không click vào con và ngược lại
            />
          </div>
        </Modal>
      </Flex>
    );
  },
);

ModalThemQuyenLoiComponent.displayName = "ModalThemQuyenLoiComponent";
export const ModalThemQuyenLoi = memo(ModalThemQuyenLoiComponent, isEqual);
