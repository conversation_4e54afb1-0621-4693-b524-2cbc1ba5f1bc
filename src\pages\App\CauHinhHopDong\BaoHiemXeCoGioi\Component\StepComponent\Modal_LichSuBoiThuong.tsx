import {forwardRef, memo, useCallback, useEffect, useMemo, useRef, useState} from "react";
import {
  DataIndexLichSuBoiThuong,
  IModalChiTietLichSuBoiThuongRef,
  ITabLichSuBoiThuongRef,
  tableLichSuBoiThuongColumn,
  TableLichSuBoiThuongDataType,
  ThemHopDongStep2_TabLichSuBoiThuongProps,
} from "./Constant";
import {Form, InputRef, Modal, Table, TableColumnType} from "antd";
import {useBaoHiemXeCoGioiContext} from "../../index.context";
import {isEqual} from "lodash";
import {Button, Highlighter, Popcomfirm, TableFilterDropdown} from "@src/components";
import {CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import {ModalChiTietLichSuBoiThuong} from "./ThemHopDongStep2_ModalChiTietLichSuBoiThuong";
import {formatCurrencyUS} from "@src/utils";
// import {ModalThemCauHoi} from "./ModalThemCauHoi";

dayjs.extend(isSameOrBefore);
// const {ma_doi_tac_ql, ten, nv} = FormChiTietDanhMucSanPham;

const ModalLichSuBoiThuongComponent = forwardRef<ITabLichSuBoiThuongRef, ThemHopDongStep2_TabLichSuBoiThuongProps>(({tabActive}: Props, ref) => {
  //   useImperativeHandle(ref, () => ({
  //     open: (dataDanhMucSanPham?: CommonExecute.Execute.IDanhMucSanPham) => {
  //       setIsOpen(true);
  //       form.resetFields();
  //       if (dataDanhMucSanPham) setChiTietDanhMucSanPham(dataDanhMucSanPham);
  //     },
  //     close: () => {
  //       setIsOpen(false);
  //       setChiTietDanhMucSanPham(null);
  //       setDanhSachCauHoi([]);
  //     },
  //   }));
  const refModalChiTietLichSuBoiThuong = useRef<IModalChiTietLichSuBoiThuongRef>(null);
  const refTabLichSuBoiThuong = useRef<ITabLichSuBoiThuongRef>(null);
  const [chiTietDanhMucSanPham, setChiTietDanhMucSanPham] = useState<CommonExecute.Execute.IDanhMucSanPham | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [pageSize, setPageSize] = useState(12);
  const {
    // loading,
    // filterParams,
    danhSachLichSuBoiThuong,
    // setFilterParams,
    chiTietNguoiDuocBaoHiem,
    layDanhSachLichSuBoiThuongHopDong,
    layChiTietLichSuBoiThuongHopDong,
    XoaLichSuBoiThuong,
  } = useBaoHiemXeCoGioiContext();

  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [formThemNgayApDung] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [ngayAdMoiTao, setNgayAdMoiTao] = useState<string | null>(null);
  // useEffect(() => {
  //   if (chiTietDanhMucSanPham) {
  //     const arrFormData = [];
  //     for (const key in chiTietDanhMucSanPham) {
  //       arrFormData.push({
  //         name: key as keyof CommonExecute.Execute.IDanhMucSanPham,
  //         value: chiTietDanhMucSanPham[key as keyof CommonExecute.Execute.IDanhMucSanPham],
  //       });
  //     }
  //     form.setFields(arrFormData);
  //   }
  // }, [chiTietDanhMucSanPham, form]);

  useEffect(() => {
    console.log("tabActive", tabActive);
    console.log("chiTietNguoiDuocBaoHiem", chiTietNguoiDuocBaoHiem);
    if (chiTietNguoiDuocBaoHiem?.so_id && chiTietNguoiDuocBaoHiem?.so_id_dt) {
      handleLoadLichSuBoiThuong();
    }
  }, [chiTietNguoiDuocBaoHiem?.so_id_dt]);

  const handleLoadLichSuBoiThuong = useCallback(async () => {
    if (!chiTietNguoiDuocBaoHiem?.so_id || !chiTietNguoiDuocBaoHiem?.so_id_dt) return;

    try {
      console.log("handleLoadLichSuBoiThuong");
      setLoading(true);
      const response = await layDanhSachLichSuBoiThuongHopDong({
        nv: "NG",
        so_id: Number(chiTietNguoiDuocBaoHiem.so_id),
        so_id_dt: Number(chiTietNguoiDuocBaoHiem.so_id_dt),
      });

      // Kiểm tra cấu trúc dữ liệu từ API
      const cauHoiData = (response as any)?.data?.cau_hoi || response?.cau_hoi;
      const giaTriDataFromApi = (response as any)?.data?.gia_tri || response?.gia_tri;

      // Lưu giaTriData vào state để sử dụng trong render
      // if (giaTriDataFromApi && Array.isArray(giaTriDataFromApi)) {
      //   setGiaTriData(giaTriDataFromApi);
      // }

      // if (cauHoiData && Array.isArray(cauHoiData)) {
      //   setListCauHoi(cauHoiData);

      //   // Khởi tạo danh sách câu trả lời với logic map giữa cauHoiData và giaTriData
      //   const initialCauTraLoi = cauHoiData.map((cauHoi: IDanhGiaSucKhoeCauHoi) => {
      //     // Xử lý ma là string hoặc mảng
      //     const maCauHoi = Array.isArray(cauHoi.ma) ? cauHoi.ma[0] : cauHoi.ma;

      //     // Tìm các giá trị tương ứng trong giaTriData dựa trên ma_cau_hoi
      //     const giaTriItems = giaTriDataFromApi?.filter((item: any) => item.ma_cau_hoi === maCauHoi) || [];

      //     let cauTraLoi = "";

      //     // Đối với TEXTAREA và NUMBER, ưu tiên lấy trực tiếp từ dap_an
      //     if (cauHoi.kieu_chon === "TEXTAREA" || cauHoi.kieu_chon === "NUMBER") {
      //       if (cauHoi.dap_an) {
      //         const dapAn = Array.isArray(cauHoi.dap_an) ? cauHoi.dap_an[0] : cauHoi.dap_an;
      //         cauTraLoi = dapAn;
      //       }
      //     } else if (giaTriItems.length > 0) {
      //       // Xử lý dap_an từ cauHoi
      //       const dapAn = Array.isArray(cauHoi.dap_an) ? cauHoi.dap_an[0] : cauHoi.dap_an;
      //       // TH1: Nếu có dap_an và khác null/rỗng
      //       if (dapAn && dapAn !== "" && dapAn !== null) {
      //         // Parse dap_an - có thể là single value hoặc multiple values với dấu pipe
      //         let dapAnValues: string[] = [];
      //         if (dapAn.includes("|")) {
      //           dapAnValues = dapAn.split("|").map(v => v.trim());
      //         } else {
      //           dapAnValues = [dapAn];
      //         }
      //         // Tìm các item trong giaTriData có gia_tri match với dapAnValues
      //         const matchedItems = giaTriItems.filter((item: any) => dapAnValues.includes(item.gia_tri));

      //         if (matchedItems.length > 0) {
      //           // Sử dụng comma để join (internal format)
      //           cauTraLoi = matchedItems.map((item: any) => item.gia_tri).join(",");
      //         } else {
      //           // Nếu không tìm thấy match nào, tìm các item có mac_dinh = "C"
      //           const defaultItems = giaTriItems.filter((item: any) => item.mac_dinh === "C");
      //           if (defaultItems.length > 0) {
      //             cauTraLoi = defaultItems.map((item: any) => item.gia_tri || item.mac_dinh).join(",");
      //           }
      //         }
      //       } else {
      //         // TH2: Nếu dap_an null/rỗng, tìm các item có mac_dinh = "C"
      //         const defaultItems = giaTriItems.filter((item: any) => item.mac_dinh === "C");
      //         if (defaultItems.length > 0) {
      //           cauTraLoi = defaultItems.map((item: any) => item.gia_tri || item.mac_dinh).join(",");
      //         } else {
      //           // Nếu không có mac_dinh = "C", lấy các giá trị đã được chọn (có gia_tri không rỗng)
      //           const selectedItems = giaTriItems.filter((item: any) => item.gia_tri && item.gia_tri !== "");
      //           if (selectedItems.length > 0) {
      //             cauTraLoi = selectedItems.map((item: any) => item.gia_tri).join(",");
      //           }
      //         }
      //       }
      //     } else {
      //       // Fallback về dap_an nếu không có trong giaTriData (cho TEXTAREA, NUMBER, INPUT)
      //       if (cauHoi.dap_an) {
      //         const dapAn = Array.isArray(cauHoi.dap_an) ? cauHoi.dap_an[0] : cauHoi.dap_an;
      //         cauTraLoi = dapAn;
      //       }
      //     }

      //     return {
      //       ma: maCauHoi,
      //       cau_tra_loi: cauTraLoi,
      //       ghi_chu: "",
      //     };
      //   });

      //   setListCauTraLoi(initialCauTraLoi);

      //   // Cập nhật ref cho các câu hỏi TEXTAREA và NUMBER
      //   initialCauTraLoi.forEach(item => {
      //     const cauHoi = cauHoiData.find((ch: any) => {
      //       const maCauHoi = Array.isArray(ch.ma) ? ch.ma[0] : ch.ma;
      //       const itemMa = Array.isArray(item.ma) ? item.ma[0] : item.ma;
      //       return maCauHoi === itemMa;
      //     });

      //     if (cauHoi && (cauHoi.kieu_chon === "TEXTAREA" || cauHoi.kieu_chon === "NUMBER") && item.cau_tra_loi) {
      //       const maCauHoi = Array.isArray(item.ma) ? item.ma[0] : item.ma;
      //       currentInputValues.current[maCauHoi] = item.cau_tra_loi;
      //     }
      //   });
      // }
    } catch (error) {
      console.error("Lỗi khi tải danh sách câu hỏi đánh giá sức khoẻ:", error);
    } finally {
      setLoading(false);
    }
  }, [chiTietNguoiDuocBaoHiem, layDanhSachLichSuBoiThuongHopDong]);

  const closeModal = () => {
    setIsOpen(false);
    setChiTietDanhMucSanPham(null);
    // setDanhSachCauHoi([]);

    form.resetFields();
    // setFilterParams(filterParams);
  };

  const dataTableLichSuBoiThuong = useMemo<Array<TableLichSuBoiThuongDataType>>(() => {
    try {
      const mappedData = danhSachLichSuBoiThuong.map((item: any, index: number) => ({
        so_id: item.so_id,
        so_id_dt: item.so_id_dt,
        ma_doi_tac_ql: item.ma_doi_tac_ql,
        nv: item.nv,
        so_hs: item.so_hs,
        ma_sp: item.ma_sp,
        lh_nv: item.lh_nv,
        tien_bt: item.tien_bt,
        tien_bt_vat: item.tien_bt_vat,
        tong_tien_bt: item.tong_tien_bt,
        ngay_mo_hs: item.ngay_mo_hs,
        ngay_duyet_bt: item.ngay_duyet_bt,
        stt: item.stt,
        sott: item.sott,
        key: index.toString(),
        hanh_dong: () => renderDeleteButton(item?.so_id, item?.so_id_dt, item?.so_hs, item?.nv),
      }));
      console.log("mappedData", mappedData);
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachLichSuBoiThuong, pageSize, chiTietNguoiDuocBaoHiem?.so_id_dt]);
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexLichSuBoiThuong) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexLichSuBoiThuong) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const handleDelete = async (so_id?: number, so_id_dt?: number, so_hs?: string, nv?: string) => {
    try {
      await XoaLichSuBoiThuong({
        so_id: so_id,
        so_id_dt: so_id_dt,
        nv: nv,
        so_hs: so_hs,
      });

      // Refresh danh sách
      if (chiTietNguoiDuocBaoHiem?.so_id_dt && chiTietNguoiDuocBaoHiem?.so_id) {
        layDanhSachLichSuBoiThuongHopDong({so_id_dt: so_id_dt, so_id: so_id, nv: "NG"});
      }
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };

  const getColumnSearchProps = (dataIndex: DataIndexLichSuBoiThuong, title: string): TableColumnType<DataIndexLichSuBoiThuong> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexLichSuBoiThuong]
        ? record[dataIndex as keyof DataIndexLichSuBoiThuong]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      // if (record.key.toString().includes("empty")) return <div style={{height: 20}} />;

      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });
  const renderDeleteButton = (so_id?: number, so_id_dt?: number, so_hs?: string, nv?: string) => {
    console.log("renderDeleteButton", so_id);
    console.log("so_id_dt", so_id_dt);
    if (!so_id || !so_id_dt) return null;
    return (
      <div onClick={e => e.stopPropagation()}>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDelete(so_id, so_id_dt, so_hs, nv)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa ngày áp dụng?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          // style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </div>
    );
  };

  const renderTableCauHoiFooter = () => {
    return (
      <Form.Item style={{marginTop: 16, marginBottom: 0, textAlign: "end"}}>
        <Button className="" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietLichSuBoiThuong.current?.open()} loading={loading} disabled={disableSubmit}>
          Thêm mới lịch sử bồi thường
        </Button>
      </Form.Item>
    );
  };
  const renderTableLichSuBoiThuong = () => {
    return (
      <Table<TableLichSuBoiThuongDataType>
        className="table-lich-su-boi-thuong no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer", overflow: "auto"}}
        onRow={(record, rowIndex) => {
          return {
            style: {cursor: "pointer"},
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const response: CommonExecute.Execute.ILichSuBoiThuongHopDong | null = await layChiTietLichSuBoiThuongHopDong({
                so_id: record.so_id,
                so_id_dt: record.so_id_dt,
              });
              if (response) refModalChiTietLichSuBoiThuong.current?.open(response);
            }, // click row
          };
        }}
        title={null}
        pagination={false}
        columns={(tableLichSuBoiThuongColumn || []).map(item => {
          if (item.key === "tien_bt" || item.key === "tien_bt_vat" || item.key === "tong_tien_bt")
            return {
              ...item,
              render: (text: any) => {
                if (typeof text === "number") return formatCurrencyUS(text);
                return text;
              },
            };
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" && item.key !== "hanh_dong" ? getColumnSearchProps(item.key as keyof TableLichSuBoiThuongDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableLichSuBoiThuong}
        bordered
        scroll={dataTableLichSuBoiThuong.length > pageSize ? {y: 285} : undefined}
      />
    );
  };

  return (
    <Modal
      title="Lịch sử bồi thường"
      open={false}
      onCancel={() => setIsOpen(false)}
      footer={null}
      width="80vw"
      style={{
        top: 0,
        left: 0,
        padding: 0,
      }}
      styles={{
        body: {
          height: "80vh",
        },
      }}>
      {" "}
      {renderTableLichSuBoiThuong()}
      {renderTableCauHoiFooter()}
      <ModalChiTietLichSuBoiThuong ref={refModalChiTietLichSuBoiThuong} />
    </Modal>
  );
});
ModalLichSuBoiThuongComponent.displayName = "ModalChiTietBoMaCauHoiComponent";
export const TabLichSuBoiThuong = memo(ModalLichSuBoiThuongComponent, isEqual);
