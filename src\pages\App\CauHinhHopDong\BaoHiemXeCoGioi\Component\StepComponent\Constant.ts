import React from "react";
import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {formatCurrencyUS, parseDateTime} from "@src/utils";
import {TableProps} from "antd";
import {LabeledValue} from "antd/es/select";
import {Tag} from "antd";

//Hàm ép kiểu dùng dung
export const parseNumber = (value: any): number => {
  // Xử lý các trường hợp null, undefined, empty string
  if (value == null || value === "") return 0;

  // Nếu đã là number, kiểm tra NaN và trả về
  if (typeof value === "number") {
    return Number.isNaN(value) ? 0 : value;
  }

  // Nếu là string, làm sạch và parse
  if (typeof value === "string") {
    // Loại bỏ khoảng trắng đầu cuối
    const trimmed = value.trim();
    if (trimmed === "") return 0;

    // Loại bỏ dấu phẩy phân cách hàng nghìn và ký tự không phải số, dấu chấm, dấu trừ
    const cleaned = trimmed.replace(/[,]/g, "").replace(/[^0-9.-]/g, "");

    // Kiểm tra format hợp lệ (chỉ có 1 dấu chấm và 1 dấu trừ ở đầu)
    if (!/^-?\d*\.?\d*$/.test(cleaned)) return 0;

    const parsed = Number(cleaned);
    return Number.isNaN(parsed) ? 0 : parsed;
  }

  // Các trường hợp khác (boolean, object, array, etc.)
  return 0;
};

//INTERFACE ĐỐI TƯỢNG
export interface TableDoiTuongColumnDataType {
  key: string;
  sott?: number; //CỘT 1
  ten?: string; //CỘT 2
  bien_xe?: string;
  so_khung?: string;
  so_may?: string;
  so_id?: number | string;
  so_id_dt?: number | string;
  tong_phi?: number | string;
  sdbs?: string; // Trạng thái đối tượng: N-Mới, S-Sửa đổi, K-Không sửa đổi, H-Kết thúc hiệu lực
  ap_dung: string;
  tl_dong?: string | number;
  tl_tai?: string | number;
  ma_dvi_dong_tai: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDoiTuongColumn: TableProps<TableDoiTuongColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "Biển xe / Số khung / Số máy",
    dataIndex: "bien_xe",
    key: "bien_xe",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Phí BH",
    dataIndex: "tong_phi",
    key: "tong_phi",
    width: "25%",
    align: "right",
    ellipsis: true,
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
    },
  },
];
export const tableDoiTuongApDungDongBHColumn: TableProps<TableDoiTuongColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: "10%",
  },
  {
    ...defaultTableColumnsProps,
    title: "Áp dụng",
    dataIndex: "ap_dung",
    key: "ap_dung",
    width: "15%",
  },
  {
    ...defaultTableColumnsProps,
    title: "Biển xe / Số khung / Số máy",
    dataIndex: "bien_xe",
    key: "bien_xe",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },

  {
    ...defaultTableColumnsProps,
    title: "Tỷ lệ",
    dataIndex: "tl_dong",
    key: "tl_dong",
    width: "20%",
  },
];
export const tableDoiTuongApDungTaiBHColumn: TableProps<TableDoiTuongColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: "10%",
  },
  {
    ...defaultTableColumnsProps,
    title: "Áp dụng",
    dataIndex: "ap_dung",
    key: "ap_dung",
    width: "15%",
  },
  {
    ...defaultTableColumnsProps,
    title: "Biển xe / Số khung / Số máy",
    dataIndex: "bien_xe",
    key: "bien_xe",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },

  {
    ...defaultTableColumnsProps,
    title: "Tỷ lệ",
    dataIndex: "tl_tai",
    key: "tl_tai",
    width: "20%",
  },
];

//keyof: return ra key của inteface TableChucDanhColumnDataType;
export type TableChucDanhColumnDataIndex = keyof TableDoiTuongColumnDataType;

//radio trong table
export const radioItemTrangThaiChucDanhTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//form tìm kiếm
//radio trong search
export const radioItemTrangThaiChucDanhSelect: Array<LabeledValue> = [
  {value: "", label: "Tất cả"},
  {value: "D", label: "Đang sử dụng"},
  {value: "K", label: "Ngưng sử dụng"},
];
export interface IFormTimKiemChucDanhFieldsConfig {
  ten: IFormInput;
  ma_doi_tac_ql: IFormInput;
  trang_thai: IFormInput;
}

export const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

//FORM THÊM /SỬA ĐỐI TƯỢNG
//INTERFACE FORM THÊM /SỬA ĐỐI TƯỢNG
export interface IFormDoiTuongBaoHiemXeCoGioi {
  so_id: IFormInput;
  so_id_dt: IFormInput;
  ten: IFormInput;
  dchi: IFormInput;
  gcn: IFormInput;
  bien_xe: IFormInput;
  so_khung: IFormInput;
  so_may: IFormInput;
  loai_xe: IFormInput;
  hang_xe: IFormInput;
  hieu_xe: IFormInput;
  nam_sx: IFormInput;
  md_sd: IFormInput;
  so_cho: IFormInput; // Required khi có quyền lợi hành khách trên xe
  so_nguoi_bh: IFormInput; // Required khi có quyền lợi hành khách trên xe
  trong_tai: IFormInput; // Required khi có quyền lợi hàng hoá trên xe
  so_lphu_xe: IFormInput; // Required khi có quyền lợi lái phụ xe
  gia_tri: IFormInput;
  gio_hl: IFormInput;
  ngay_hl: IFormInput;
  gio_kt: IFormInput;
  ngay_kt: IFormInput;
  ngay_cap: IFormInput;
  vip: IFormInput;
  dk: IFormInput;
  dkbs: IFormInput;
}

//KHỞI TẠO FORM THÊM /SỬA ĐỐI TƯỢNG
export const FormTaoMoiDoiTuongBaoHiemXe: IFormDoiTuongBaoHiemXeCoGioi = {
  so_id: {
    // component: "input",
    // label: "Số ID",
    name: "so_id",
    // placeholder: "Nhập số ID",
    // rules: [ruleRequired],
    className: "hidden",
  },
  so_id_dt: {
    // component: "input",
    // label: "Số ID đối tác",
    name: "so_id_dt",
    // placeholder: "Nhập số ID đối tác",
    // rules: [ruleRequired],
    className: "hidden",
  },
  ten: {
    component: "input",
    label: "Tên chủ xe",
    name: "ten",
    placeholder: "Nhập tên chủ xe",
    rules: [ruleRequired],
  },
  dchi: {
    component: "input",
    label: "Địa chỉ",
    name: "dchi",
    placeholder: "Nhập địa chỉ",
    rules: [ruleRequired],
  },
  gcn: {
    component: "input",
    label: "Giấy chứng nhận",
    name: "gcn",
    placeholder: "Nhập giấy chứng nhận",
    rules: [ruleRequired],
    normalize: (value: string) => value?.toUpperCase(),
  },
  bien_xe: {
    component: "input",
    label: "Biển xe",
    name: "bien_xe",
    placeholder: "Nhập biển xe",
    // rules: [ruleRequired],
    normalize: (value: string) => value?.toUpperCase(),
  },
  so_khung: {
    component: "input",
    label: "Số khung",
    name: "so_khung",
    placeholder: "Nhập số khung",
    rules: [ruleRequired],
    normalize: (value: string) => value?.toUpperCase(),
  },
  so_may: {
    component: "input",
    label: "Số máy",
    name: "so_may",
    placeholder: "Nhập số máy",
    rules: [ruleRequired],
    normalize: (value: string) => value?.toUpperCase(),
  },
  loai_xe: {
    component: "select",
    label: "Loại xe",
    name: "loai_xe",
    placeholder: "Chọn loại xe",
    rules: [ruleRequired],
  },
  hang_xe: {
    component: "select",
    label: "Hãng xe",
    name: "hang_xe",
    placeholder: "Chọn hãng xe",
    rules: [ruleRequired],
  },
  hieu_xe: {
    component: "select",
    label: "Hiệu xe",
    name: "hieu_xe",
    placeholder: "Chọn hiệu xe",
    rules: [ruleRequired],
  },
  nam_sx: {
    component: "select",
    label: "Năm sản xuất",
    name: "nam_sx",
    placeholder: "Chọn năm sản xuất",
    rules: [ruleRequired],
  },
  md_sd: {
    component: "select",
    label: "Mục đích sử dụng",
    name: "md_sd",
    placeholder: "Chọn mục đích sử dụng",
    rules: [ruleRequired],
  },
  so_cho: {
    component: "input",
    label: "Số chỗ",
    name: "so_cho",
    placeholder: "Nhập số chỗ",
  },
  so_nguoi_bh: {
    component: "input",
    label: "Số người bảo hiểm",
    name: "so_nguoi_bh",
    placeholder: "Nhập số người bảo hiểm",
  },
  trong_tai: {
    component: "input",
    label: "Trọng tải (tấn)",
    name: "trong_tai",
    placeholder: "Nhập trọng tải",
    // rules: [ruleRequired],
  },
  so_lphu_xe: {
    component: "input",
    label: "Số lái phụ xe",
    name: "so_lphu_xe",
    placeholder: "Nhập số lái phụ xe",
  },
  gia_tri: {
    component: "input-price",
    label: "Giá trị",
    name: "gia_tri",
    placeholder: "0",
    rules: [ruleRequired],
  },
  gio_hl: {
    component: "time-picker",
    label: "Giờ hiệu lực",
    name: "gio_hl",
    placeholder: "Chọn giờ",
    className: "w-full",
    rules: [ruleRequired],
  },
  ngay_hl: {
    component: "date-picker",
    label: "Ngày hiệu lực",
    name: "ngay_hl",
    placeholder: "Chọn ngày",
    className: "w-full",
    rules: [ruleRequired],
  },
  gio_kt: {
    component: "time-picker",
    label: "Giờ kết thúc",
    name: "gio_kt",
    className: "w-full",
    placeholder: "Chọn giờ",
    rules: [ruleRequired],
  },
  ngay_kt: {
    component: "date-picker",
    label: "Ngày kết thúc",
    name: "ngay_kt",
    className: "w-full",
    placeholder: "Chọn ngày",
    rules: [ruleRequired],
  },
  ngay_cap: {
    component: "date-picker",
    label: "Ngày cấp",
    name: "ngay_cap",
    placeholder: "Chọn ngày",
    className: "w-full",
    rules: [ruleRequired],
  },
  vip: {
    component: "select",
    label: "Hợp đồng VIP",
    name: "vip",
    placeholder: "Chọn hợp đồng VIP",
  },
  dk: {
    name: "dk",
    className: "hidden",
  },
  dkbs: {
    name: "dkbs",
    className: "hidden",
  },
};

//option select năm sản xuất
export const listNamSanXuat = Array.from({length: 41}, (_, index) => {
  const currentYear = new Date().getFullYear();
  const year = currentYear - index;
  return {
    ten: year.toString(),
    ma: year.toString(),
  };
});

//option select mục đích sử dụng
export const listMucDichSuDungXe = [
  {ten: "Kinh doanh", ma: "C"},
  {ten: "Không kinh doanh", ma: "K"},
];

//option select kiểu đồng
export const listKieuTai = [
  {ten: "Tái cố định", ma: "C"},
  {ten: "Tái tạm thời", ma: "T"},
];

//option select đối tượng áp dụng
export const listDoiTuongApDungDongBH = [
  {ten: "Tất cả", ma: ""},
  {ten: "Đối tượng áp dụng", ma: "DONG_BH"},
];
export const listDoiTuongApDungTaiBH = [
  {ten: "Tất cả", ma: ""},
  {ten: "Đối tượng áp dụng", ma: "TAI_BH"},
];

//FORM TÌM KIẾM ĐỐI TƯỢNG
//INTERFACE FORM TÌM KIẾM ĐỐI TƯỢNG
export interface IFormTimKiemPhanTrangDoiTuongBaoHiemXeCoGioiFieldsConfig {
  so_id?: IFormInput;
  gcn?: IFormInput;
  ten?: IFormInput;
  nd_tim?: IFormInput;
  dong_tai?: IFormInput;
}

// KHƠI TẠO FORM TÌM KIẾM ĐỐI TƯỢNG
export const FormTimKiemDoiTuongBaoHiemXeCoGioi: IFormTimKiemPhanTrangDoiTuongBaoHiemXeCoGioiFieldsConfig = {
  so_id: {
    name: "so_id",
  },
  gcn: {
    component: "input",
    label: "Số GCN",
    name: "gcn",
    open: false,
    placeholder: "Nhập số GCN",
  },
  ten: {
    component: "input",
    label: "Tên đối tượng",
    name: "ten",
    placeholder: "Tên đối tượng",
  },
  nd_tim: {
    component: "input",
    // label: "Số GCN/BSX/số khung/số máy",
    name: "nd_tim",
    placeholder: "Số GCN/BSX/số khung/số máy",
  },
  dong_tai: {
    component: "select",
    // label: "Đối tượng áp dụng",
    name: "dong_tai",
    placeholder: "Chọn đối tượng áp dụng",
    options: listDoiTuongApDungDongBH,
  },
};

export const FormTimKiemDoiTuongTaiBaoHiem: IFormTimKiemPhanTrangDoiTuongBaoHiemXeCoGioiFieldsConfig = {
  so_id: {
    name: "so_id",
  },
  gcn: {
    component: "input",
    label: "Số GCN",
    name: "gcn",
    open: false,
    placeholder: "Nhập số GCN",
  },
  ten: {
    component: "input",
    label: "Tên đối tượng",
    name: "ten",
    placeholder: "Tên đối tượng",
  },
  nd_tim: {
    component: "input",
    // label: "Số GCN/BSX/số khung/số máy",
    name: "nd_tim",
    placeholder: "Số GCN/BSX/số khung/số máy",
  },
  dong_tai: {
    component: "select",
    // label: "Đối tượng áp dụng",
    name: "dong_tai",
    placeholder: "Chọn đối tượng áp dụng",
    options: listDoiTuongApDungTaiBH,
  },
};

//khởi tạo form chi tiết đối tượng
export const initFormFieldsDoiTuong = (form: any, chiTietDoiTuongBaoHiemXe: any) => {
  if (!chiTietDoiTuongBaoHiemXe) return;
  const {gcn, gcn_dk, gcn_dkbs} = chiTietDoiTuongBaoHiemXe;

  const DATE_TIME_FIELDS = ["ngay_cap", "ngay_hl", "ngay_kt", "gio_hl", "gio_kt"];

  const fields = Object.entries(gcn || {}).map(([name, value]) => {
    if (!value) return {name, value: ""};
    //formart ngày
    if (DATE_TIME_FIELDS.includes(name)) {
      return {
        name,
        value: parseDateTime(value),
        errors: [],
      };
    }

    return {name, value, errors: []};
  });

  // Set fields from gcn
  form.setFields(fields);

  // Set fields from gcn_dk
  if (gcn_dk) {
    form.setFieldsValue({dk: gcn_dk});
    form.setFields([{name: "dk", touched: true}]);
  }

  // Set fields from gcn_dkbs
  if (gcn_dkbs) {
    form.setFieldsValue({dkbs: gcn_dkbs});
    form.setFields([{name: "dkbs", touched: true}]);
  }
};

///LOẠI HÌNH NGHIỆP VỤ
///BẢNG LHNV I
export interface TableLoaiHinhNghiepVuDataType {
  key: string;
  stt: number;
  ten: string;
  tien: number;
  phi: number;
  thue: number;
  mien_thuong: number;
  ktru: string;
  doi_tuong: string;
  loai: string;
  ma: string;
  ma_doi_tac: string;
  ma_doi_tac_ql: string;
  nhom: string;
  nv: string;
  lh_nv: string;
  tong_phi: number;
  ma_sp: string;
  phi_giam: number;
  thue_giam: number;
}
//BẢNG LHNV
export const tableLoaiHinhNghiepVuColumns: TableProps<TableLoaiHinhNghiepVuDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center"},
  {...defaultTableColumnsProps, title: "Sản phẩm", dataIndex: "ten", key: "ten", width: 200, align: "left"},
  {
    ...defaultTableColumnsProps,
    title: "Tiền bảo hiểm",
    dataIndex: "tien",
    key: "tien",
    width: 140,
    align: "right",
  },

  {
    ...defaultTableColumnsProps,
    title: "K/trừ",
    dataIndex: "ktru",
    key: "ktru",
    width: 60,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Miễn thường",
    dataIndex: "mien_thuong",
    key: "mien_thuong",
    width: 140,
    align: "right",
  },
  {
    ...defaultTableColumnsProps,
    title: "Phí bảo hiểm",
    dataIndex: "phi",
    key: "phi",
    width: 140,
    align: "right",
  },
  {
    ...defaultTableColumnsProps,
    title: "Thuế",
    dataIndex: "thue",
    key: "thue",
    width: 140,
    align: "right",
  },
  {
    ...defaultTableColumnsProps,
    title: "Phí giảm",
    dataIndex: "phi_giam",
    key: "phi_giam",
    width: 140,
    align: "right",
  },
  {
    ...defaultTableColumnsProps,
    title: "Thuế giảm",
    dataIndex: "thue_giam",
    key: "thue_giam",
    width: 140,
    align: "right",
  },
  {
    ...defaultTableColumnsProps,
    title: "Tổng phí",
    dataIndex: "tong_phi",
    key: "tong_phi",
    width: 140,
    align: "right",
  },
];

////ĐIỀU KHOẢN BỔ SUNG
///BẢNG DKBS I
export interface TableDieuKhoanBoSungDataType {
  key: string;
  stt: number;
  ten: string;
  tien: number;
  phi: number;
  thue: number;
  loai: string;
  ma: string;
  ma_doi_tac: string;
  ma_doi_tac_ql: string;
  nv: string;
  tong_phi: number;
  t_gia: string;
  phi_giam: number;
  thue_giam: number;
}
//BẢNG LHNV
export const tableDieuKhoanBoSungColumns: TableProps<TableDieuKhoanBoSungDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center"},
  {
    ...defaultTableColumnsProps,
    title: "Tham gia",
    dataIndex: "t_gia",
    key: "t_gia",
    width: 80,
    align: "center",
  },
  {...defaultTableColumnsProps, title: "Điều khoản bổ sung", dataIndex: "ten", key: "ten", width: 200, align: "left"},

  {
    ...defaultTableColumnsProps,
    title: "Phí bảo hiểm",
    dataIndex: "phi",
    key: "phi",
    width: 150,
    align: "right",
  },
  {
    ...defaultTableColumnsProps,
    title: "Thuế",
    dataIndex: "thue",
    key: "thue",
    width: 150,
    align: "right",
  },
  {
    ...defaultTableColumnsProps,
    title: "Phí giảm",
    dataIndex: "phi_giam",
    key: "phi_giam",
    width: 150,
    align: "right",
  },
  {
    ...defaultTableColumnsProps,
    title: "Thuế giảm",
    dataIndex: "thue_giam",
    key: "thue_giam",
    width: 150,
    align: "right",
  },
  {
    ...defaultTableColumnsProps,
    title: "Tổng phí",
    dataIndex: "tong_phi",
    key: "tong_phi",
    width: 150,
    align: "right",
  },
];

//THÔNG TIN THANH TOÁN
//INTERFACE TT THANH TOÁN
export interface TableThongTinKyThanhToanDataType {
  bt: number;
  ky_tt: number;
  ky_tt_date: string;
  loai: string;
  loai_ten: string | null;
  ma_doi_tac: string;
  ma_doi_tac_ql: string;
  ngay_cap_nhat: string | null;
  ngay_tao: string | null;
  ngay_tt: string | null;
  ngay_tt_date: string;
  nguoi_cap_nhat: string | null;
  nguoi_tao: string | null;
  so_hd: string | null;
  so_id: number | null;
  so_id_d: number;
  so_id_g: number | null;
  so_id_ky_ttoan: number;
  so_tien: number;
  so_tien_da_tt: number | null;
  trang_thai: string | null;
  key: string;
  sott: number;
  rowSpanKyTT?: number;
  rowSpanSoTien?: number;
  sttKyTT?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableThongTinThanhToanColumn: TableProps<TableThongTinKyThanhToanDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sttKyTT",
    key: "sttKyTT",
    width: colWidthByKey.sott,
    align: "center",
    onCell: (record: any) => ({rowSpan: record.rowSpanKyTT ?? 1, className: "!py-1 text-[11px]"}),
    render: (text: any, record: any) => (record.sttKyTT ? record.sttKyTT : null),
  },
  {
    ...defaultTableColumnsProps,
    title: "Kỳ t/toán",
    dataIndex: "ky_tt_date",
    key: "ky_tt_date",
    width: 90,
    align: "center",
    onCell: (record: any) => ({rowSpan: record.rowSpanKyTT ?? 1, className: "!py-1 text-[11px] font-semibold"}),
    render: (text: any, record: any, index: number) => {
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px] "}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Số tiền",
    dataIndex: "so_tien",
    key: "so_tien",
    width: 110,
    align: "right",
    ellipsis: {showTitle: false},
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
    onCell: (record: any) => ({rowSpan: record.rowSpanSoTien ?? 1, className: "!py-1 text-[11px] !font-semibold"}),
  },
  {
    ...defaultTableColumnsProps,
    title: "Loại",
    dataIndex: "loai_ten",
    key: "loai_ten",
    width: 80,
    align: "center",
    render: (text: any, record: any) => {
      if (text === "Tăng phí") {
        return React.createElement("span", {style: {color: "#52c41a"}}, text);
      }
      if (text === "Hoàn phí") {
        return React.createElement("span", {style: {color: "#ff4d4f"}}, text);
      }
      return record.loai_ten;
    },
  },
  {...defaultTableColumnsProps, title: "Ngày t/toán", dataIndex: "ngay_tt_date", key: "ngay_tt_date", width: 100, align: "center"},
  {
    ...defaultTableColumnsProps,
    title: "Tiền đã t/toán",
    dataIndex: "so_tien_da_tt",
    key: "so_tien_da_tt",
    width: 120,
    align: "right",
    ellipsis: {showTitle: false},
    render: (text: any) => {
      if (typeof text === "number") {
        if (text < 0) {
          return React.createElement("span", {style: {color: "#ff4d4f"}}, formatCurrencyUS(text));
        }
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {...defaultTableColumnsProps, title: "Số chứng từ", dataIndex: "so_ct", key: "so_ct", width: 120, align: "center"},
  {...defaultTableColumnsProps, title: "Số HĐ/SĐBS", dataIndex: "so_hd", key: "so_hd", width: 120, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center"},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center"},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center"},
];

export interface IFormNhapKyThanhToan {
  ky_tt: IFormInput;
  so_tien: IFormInput;
  so_hd_d: IFormInput;
}
export const formNhapKyThanhToanInputConfigs: IFormNhapKyThanhToan = {
  ky_tt: {
    name: "ky_tt",
    component: "date-picker",
    label: "Kỳ thanh toán",
    placeholder: "Chọn kỳ thanh toán",
    rules: [ruleRequired],
  },
  so_tien: {
    name: "so_tien",
    component: "input-price",
    label: "Số tiền",
    placeholder: "0",
    rules: [ruleRequired],
  },
  so_hd_d: {
    name: "so_hd_d",
    component: "input",
    label: "Số HĐ đầu",
    placeholder: "Nhập số HĐ đầu",
    rules: [ruleRequired],
    disabled: true,
  },
};

//TAB NGÀY THANH TOÁN
export interface TableNgayThanhToanDataType {
  key: string;
  stt: number;
  ngay_tt: string;
  so_tien_da_tt: number;
  bt: number;
  ten_loai: string;
  so_ct: string;
}

export const tableNgayThanhToanColumns: TableProps<TableNgayThanhToanDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, className: "no-hover-table"},
  {...defaultTableColumnsProps, title: "Ngày thanh toán", dataIndex: "ngay_tt", key: "ngay_tt", className: "no-hover-table"},
  {...defaultTableColumnsProps, title: "Số chứng từ", dataIndex: "so_ct", key: "so_ct", width: 150, className: "no-hover-table"},
  {
    ...defaultTableColumnsProps,
    title: "Số tiền đã thanh toán",
    dataIndex: "so_tien_da_tt",
    key: "so_tien_da_tt",
    width: 150,
    align: "right",
    ellipsis: true,
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },

  {...defaultTableColumnsProps, title: "Tên loại", dataIndex: "ten_loai", key: "ten_loai", width: 80},
  {
    ...defaultTableColumnsProps,
    // title: "Hành động",
    dataIndex: "action",
    key: "action",
    width: 80,
  },
];

//THÔNG TIN ĐỒNG BẢO HIỂM TABLE

//INTERFACE TT IN ĐỒNG BẢO HIỂM TABLE
export interface TableCauHinhDongBaoHiemDataType {
  kieu_dong?: string;
  loai_dong?: string;
  loai_dong_ten?: string;
  ma_doi_tac?: string;
  ma_doi_tac_ql?: string;
  ma_dvi_dong?: string;
  ten_dvi_dong?: string;
  nv?: string;
  sl_dt_bh?: number;
  sl_goi_bh?: number;
  sl_qloi_bh?: number;
  tl_dong?: number;
  sott?: number;
  key: string;
  so_tham_chieu?: string;
  tien_nhuong_dong?: number;
  tien_con?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableCauHinhDongBaoHiemColumn: TableProps<TableCauHinhDongBaoHiemDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
  },
  {
    ...defaultTableColumnsProps,
    title: "Loại đồng",
    dataIndex: "loai_dong_ten",
    key: "loai_dong_ten",
    width: 90,

    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Kiểu đồng",
    dataIndex: "kieu_dong",
    key: "kieu_dong",
    width: 90,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên đơn vị đồng",
    dataIndex: "ten_dvi_dong",
    key: "ten_dvi_dong",
  },
  {
    ...defaultTableColumnsProps,
    title: "TL đồng (%)",
    dataIndex: "tl_dong",
    key: "tl_dong",
    width: 100,
  },
  {
    ...defaultTableColumnsProps,
    title: "Số tham chiếu/Hợp đồng/SĐBS",
    dataIndex: "so_tham_chieu",
    key: "so_tham_chieu",
    width: 200,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tiền nhượng đồng",
    dataIndex: "tien_nhuong_dong",
    key: "tien_nhuong_dong",
    width: 150,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Tiền giữ lại",
    dataIndex: "tien_con",
    key: "tien_con",
    width: 150,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {...defaultTableColumnsProps, title: "Đối tượng áp dụng", dataIndex: "sl_dt_bh", key: "sl_dt_bh", align: "center", width: 200},
  {
    ...defaultTableColumnsProps,
    // title: "Hành động",
    dataIndex: "action",
    key: "action",
    width: 60,
  },
];

export interface IFormNhapCauHinhDong {
  ma_dvi_dong: IFormInput;
  loai_dong: IFormInput;
  kieu_dong: IFormInput;
  so_id: IFormInput;
  tl_dong: IFormInput;
  so_tham_chieu: IFormInput;
}
export const formCauHinhDongInputConfigs: IFormNhapCauHinhDong = {
  so_id: {
    name: "so_id",
    className: "hidden",
  },
  ma_dvi_dong: {
    name: "ma_dvi_dong",
    component: "select",
    label: "Mã đơn vị đồng",
    placeholder: "Mã đơn vị",
    rules: [ruleRequired],
  },
  loai_dong: {
    name: "loai_dong",
    component: "select",
    label: "Loại đồng",
    placeholder: "Chọn loại đồng",
    rules: [ruleRequired],
  },
  kieu_dong: {
    name: "kieu_dong",
    component: "select",
    label: "Kiểu đồng NBH",
    placeholder: "Nhập số HĐ đầu",
    rules: [ruleRequired],
  },
  tl_dong: {
    name: "tl_dong",
    component: "input",
    label: "Tỷ lệ đồng",
    placeholder: "0",
    className: "!text-right",
    rules: [ruleRequired],
  },
  so_tham_chieu: {
    name: "so_tham_chieu",
    component: "input",
    label: "Số tham chiếu/Hợp đồng/SĐBS",
    placeholder: "Số tham chiếu/Hợp đồng/SĐBS",
    rules: [ruleRequired],
  },
};
//option select loại đồng
export const listLoaiDong = [
  {ten: "Đồng trong", ma: "T"},
  {ten: "Đồng ngoài", ma: "N"},
];

//option select kiểu đồng
export const listKieuDong = [
  {ten: "LEADER", ma: "LEADER"},
  {ten: "FOLLOW", ma: "FOLLOW"},
];

//THÔNG TIN TÁI BẢO HIỂM TABLE

//INTERFACE TT IN ĐỒNG BẢO HIỂM TABLE
export interface TableCauHinhTaiBHDataType {
  key?: string | number;
  kieu_tai?: string;
  kieu_tai_ten?: string;
  ma_doi_tac?: string;
  ma_doi_tac_ql?: string;
  ma_dvi_tai?: string;
  ten_dvi_tai?: string;
  nv?: string;
  sl_dt_bh?: number;
  sl_goi_bh?: number;
  sl_qloi_bh?: number;
  sott?: number;
  tl_tai?: number;
  so_tham_chieu?: string;
  tien_nhuong_tai?: number;
  tien_con?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableCauHinhTaiBHColumn: TableProps<TableCauHinhTaiBHDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
  },

  {
    ...defaultTableColumnsProps,
    title: "Kiểu tái",
    dataIndex: "kieu_tai_ten",
    key: "kieu_tai_ten",
    width: 100,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên đơn vị tái",
    dataIndex: "ten_dvi_tai",
    key: "ten_dvi_tai",
  },
  {
    ...defaultTableColumnsProps,
    title: "TL tái (%)",
    dataIndex: "tl_tai",
    key: "tl_tai",
    width: 100,
  },
  {
    ...defaultTableColumnsProps,
    title: "Số tham chiếu/Hợp đồng/SĐBS",
    dataIndex: "so_tham_chieu",
    key: "so_tham_chieu",
    width: 250,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tiền nhượng tái",
    dataIndex: "tien_nhuong_tai",
    key: "tien_nhuong_tai",
    width: 150,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  // {
  //   ...defaultTableColumnsProps,
  //   title: "Tiền giữ lại",
  //   dataIndex: "tien_con",
  //   key: "tien_con",
  //   width: 150,
  //   align: "right",
  //   render: (text: any) => {
  //     if (typeof text === "number") {
  //       return formatCurrencyUS(text);
  //     }
  //     return text;
  //   },
  // },
  {...defaultTableColumnsProps, title: "Đối tượng áp dụng", dataIndex: "sl_dt_bh", key: "sl_dt_bh", align: "center", width: 200},
  {
    ...defaultTableColumnsProps,
    // title: "Hành động",
    dataIndex: "action",
    key: "action",
    width: colWidthByKey.sott,
  },
];

export interface IFormNhapCauHinhTaiBH {
  ma_dvi_tai: IFormInput;
  kieu_tai: IFormInput;
  so_id: IFormInput;
  tl_tai: IFormInput;
  so_tham_chieu: IFormInput;
}
export const formCauHinhTaiBHInputConfigs: IFormNhapCauHinhTaiBH = {
  so_id: {
    name: "so_id",
    className: "hidden",
  },
  ma_dvi_tai: {
    name: "ma_dvi_tai",
    component: "select",
    label: "Mã đơn vị tái",
    placeholder: "Mã đơn vị",
    rules: [ruleRequired],
  },
  kieu_tai: {
    name: "kieu_tai",
    component: "select",
    label: "Kiểu tái NBH",
    placeholder: "Nhập số HĐ đầu",
    rules: [ruleRequired],
  },
  tl_tai: {
    name: "tl_tai",
    component: "input",
    label: "Tỷ lệ tái",
    placeholder: "0",
    className: "!text-right",
    rules: [ruleRequired],
  },
  so_tham_chieu: {
    name: "so_tham_chieu",
    component: "input",
    label: "Số tham chiếu/Hợp đồng/SĐBS",
    placeholder: "Số tham chiếu/Hợp đồng/SĐBS",
    rules: [ruleRequired],
  },
};

// MODAL ĐÁNH GIÁ TỔN THẤT
export interface IModalDanhGiaTonThatRef {
  open: (params: ReactQuery.IChiTietDoiTuongBaoHiemXeParams) => void;
  close: () => void;
}

export interface TableDanhGiaTonThatDataType {
  key: string;
  stt: number;
  ten_hang_muc: string;
  ten_muc_do_tt: string;
  ma_hang_muc: string;
  ma_muc_do_tt?: string;
  ghi_chu?: string;
}

export const tableDanhGiaTonThatColumns: TableProps<TableDanhGiaTonThatDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: colWidthByKey.sott,
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã hạng mục",
    dataIndex: "ma_hang_muc",
    key: "ma_hang_muc",
    width: 140,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên hạng mục",
    dataIndex: "ten_hang_muc",
    key: "ten_hang_muc",
    align: "left",
  },

  {
    ...defaultTableColumnsProps,
    title: "Mức độ tổn thất",
    dataIndex: "ten_muc_do_tt",
    key: "ten_muc_do_tt",
    width: 180,
  },
  {
    ...defaultTableColumnsProps,
    title: "Ghi chú",
    dataIndex: "ghi_chu",
    key: "ghi_chu",
    width: 80,
  },
  {
    ...defaultTableColumnsProps,
    title: "",
    dataIndex: "action",
    key: "action",
    width: 80,
  },
];

export interface TableHangMucTonThatColumnDataType {
  key: string;
  sott?: number; //CỘT 1
  ten?: string; //CỘT 2
  ma?: string;
  ap_dung?: string;
  vi_tri_ten?: string;
}

export const tableHangMucTonThatColumn: TableProps<TableHangMucTonThatColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
  },
  {
    ...defaultTableColumnsProps,
    title: "Áp dụng",
    dataIndex: "ap_dung",
    key: "ap_dung",
    width: 80,
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã hạng mục",
    dataIndex: "ma",
    key: "ma",
    width: 140,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên hạng mục",
    dataIndex: "ten",
    key: "ten",
    align: "left",
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },

  {
    ...defaultTableColumnsProps,
    title: "Vị trí",
    dataIndex: "vi_tri_ten",
    key: "vi_tri_ten",
    width: 120,
  },
];

export interface IFormTimKiemHangMucTonThatFieldsConfig {
  nv: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  loai: IFormInput;
  nhom: IFormInput;
  vi_tri: IFormInput;
  trang_thai: IFormInput;
}

export const FormTimKiemHangMucTonThat: IFormTimKiemHangMucTonThatFieldsConfig = {
  nv: {
    component: "input",
    name: "nv",
    placeholder: "Nghiệp vụ",
  },
  ma: {
    component: "input",
    name: "ma",
    placeholder: "Mã",
  },
  ten: {
    component: "input",
    name: "ten",
    placeholder: "Tên",
  },
  loai: {
    component: "input",
    name: "loai",
    placeholder: "Loại",
  },
  nhom: {
    component: "input",
    name: "nhom",
    placeholder: "Nhóm",
  },
  vi_tri: {
    component: "input",
    name: "vi_tri",
    placeholder: "Vị trí",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    placeholder: "Trạng thái",
  },
};
// MODAL LỊCH SỬ BỒI THƯỜNG
export interface ThemHopDongStep2_TabLichSuBoiThuongRef {
  // openModalChiTietNguoiPhuThuoc: () => void;
  // getListQuyenLoiNguoiDuocBaoHiem: () => CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  // getCheckboxApDungGoiBaohiem: () => boolean;
}
export interface ITabLichSuBoiThuongRef {
  openModalThemCauHoi: () => void;
}
export interface ThemHopDongStep2_TabLichSuBoiThuongProps {
  tabActive: string;
}
//bảng lịch sử bồi thường
export interface TableLichSuBoiThuongDataType {
  key: string;
  sott?: number;
  so_id?: number;
  so_id_dt?: number;

  ma_doi_tac_ql?: string;
  nv?: string;
  so_hs?: string;
  ma_sp?: string;
  lh_nv?: string;
  tien_bt?: number;
  tien_bt_vat?: number;
  tong_tien_bt?: number;
  ngay_mo_hs?: number;
  ngay_duyet_bt?: number;
  hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là JSX.Element;
}
export const tableLichSuBoiThuongColumn: TableProps<TableLichSuBoiThuongDataType>["columns"] = [
  // {title: "STT", dataIndex: "sott", key: "sott", align: "center", width: colWidthByKey.sott, ...defaultTableColumnsProps},

  {title: "Số hồ sơ", dataIndex: "so_hs", key: "so_hs", width: 150, ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "ma_doi_tac_ql", key: "ma_doi_tac_ql", width: 150, ...defaultTableColumnsProps},
  {title: "Mã sản phẩm", dataIndex: "ma_sp", key: "ma_sp", width: 150, ...defaultTableColumnsProps},
  {title: "LH.NV", dataIndex: "lh_nv", key: "lh_nv", width: 150, ...defaultTableColumnsProps},
  {
    title: "Tiền bồi thường",
    dataIndex: "tien_bt",
    key: "tien_bt",
    render: (text: any) => {
      if (typeof text === "number") return formatCurrencyUS(text);
      return text;
    },
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tiền bồi thường VAT",
    dataIndex: "tien_bt_vat",
    key: "tien_bt_vat",
    render: (text: any) => {
      if (typeof text === "number") return formatCurrencyUS(text);
      return text;
    },
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tổng tiền bồi thường",
    dataIndex: "tong_tien_bt",
    key: "tong_tien_bt",
    render: (text: any) => {
      if (typeof text === "number") return formatCurrencyUS(text);
      return text;
    },
    width: 150,
    ...defaultTableColumnsProps,
  },
  {title: "Ngày mở hồ sơ", dataIndex: "ngay_mo_hs", key: "ngay_mo_hs", width: 150, ...defaultTableColumnsProps},
  {title: "Ngày duyệt bồi thường", dataIndex: "ngay_duyet_bt", key: "ngay_duyet_bt", width: 150, ...defaultTableColumnsProps},
  {
    title: "Xóa",
    dataIndex: "hanh_dong",
    key: "hanh_dong",
    width: 50,
    align: "center",
    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type DataIndexLichSuBoiThuong = keyof TableLichSuBoiThuongDataType;

export interface IModalChiTietLichSuBoiThuongRef {
  open: (data?: any) => void;
  close: () => void;
}
export interface ChiTietLichSuBoiThuongProps {
  chiTietHopDong: CommonExecute.Execute.IHopDongConNguoi | null;
  listNguoiPhuThuoc: CommonExecute.Execute.IChiTietNguoiPhuThuoc[];
  initListNguoiPhuThuoc: () => void;
}
export interface IFormChiTietLichSuBoiThuongFieldsConfig {
  // so_hd?: IFormInput;
  tien_dp?: IFormInput;
  so_hs?: IFormInput;
  ma_sp?: IFormInput;
  lh_nv?: IFormInput;
  tien_bt?: IFormInput;
  tien_bt_vat?: IFormInput;
  tong_tien_bt?: IFormInput;
  ngay_mo_hs?: IFormInput;
  ngay_duyet_bt?: IFormInput;
  ma_doi_tac_ql?: IFormInput;
}
export const FormChiTietLichSuBoiThuong: IFormChiTietLichSuBoiThuongFieldsConfig = {
  // so_hd: {
  //   name: "so_hd",
  //   component: "input",
  //   label: "Số hợp đồng",
  //   placeholder: "Nhập số hợp đồng",
  //   rules: [ruleRequired],
  //   // disabled: true,
  // },
  so_hs: {
    name: "so_hs",
    component: "input",
    label: "Số hồ sơ",
    placeholder: "Nhập số hồ sơ",
    rules: [ruleRequired],
    // disabled: true,
  },
  ma_doi_tac_ql: {
    name: "ma_doi_tac_ql",
    component: "select",
    label: "Đối tác quản lý",
    placeholder: "Nhập đối tác quản lý",
    rules: [ruleRequired],
    // disabled: true,
  },
  ma_sp: {
    name: "ma_sp",
    component: "select",
    label: "Mã sản phẩm",
    placeholder: "Nhập mã sản phẩm",
    rules: [ruleRequired],
    // disabled: true,
  },
  lh_nv: {
    name: "lh_nv",
    component: "select",
    label: "Loại hình nghiệp vụ/ Quyền lợi",
    placeholder: "Nhập LH.NV",
    rules: [ruleRequired],
    // disabled: true,
  },
  tien_dp: {
    name: "tien_dp",
    component: "input-price",
    label: "Tiền địa phương",
    placeholder: "Nhập tiền địa phương",
    rules: [ruleRequired],
    // disabled: true,
  },
  tien_bt: {
    name: "tien_bt",
    component: "input-price",
    label: "Tiền bồi thường",
    placeholder: "Nhập tiền bồi thường",
    rules: [ruleRequired],
    // disabled: true,
  },
  tien_bt_vat: {
    name: "tien_bt_vat",
    component: "input-price",
    label: "Tiền bồi thường VAT",
    placeholder: "Nhập tiền bồi thường VAT",
    rules: [ruleRequired],
    // disabled: true,
  },
  tong_tien_bt: {
    name: "tong_tien_bt",
    component: "input-price",
    label: "Tổng tiền bồi thường",
    placeholder: "Tự động tính từ tiền bồi thường + VAT",
    rules: [ruleRequired],
    disabled: true, // Tự động tính toán, không cho phép nhập
  },
  ngay_mo_hs: {
    name: "ngay_mo_hs",
    component: "date-picker",
    label: "Ngày mở hồ sơ",
    placeholder: "Chọn ngày mở hồ sơ",
    rules: [ruleRequired],
    // disabled: true,
  },
  ngay_duyet_bt: {
    name: "ngay_duyet_bt",
    component: "date-picker",
    label: "Ngày duyệt bồi thường",
    placeholder: "Chọn ngày duyệt bồi thường",
    rules: [ruleRequired],
    // disabled: true,
  },
};
