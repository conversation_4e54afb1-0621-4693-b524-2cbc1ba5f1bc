/* Modal <PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> viện Styles */
.modal-cau-hinh-benh-vien {

  .no-header-border-radius .ant-table-thead > tr > th {
    border-radius: 0 !important;
    background-color: #96bf49 !important;
    color: #fff !important;
    font-weight: bold !important;
    text-align: center !important;
  }

  .ant-table-row:hover td {
    background-color: #e8f5e9 !important;
  }

  .ant-table-thead > tr > th {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
  }

  /* Custom checkbox styling ONLY in header */
  .ant-table-thead .ant-checkbox-wrapper {
    color: #fff !important;
  }

  .ant-table-thead .ant-checkbox-wrapper .ant-checkbox-inner {
    border-color: #fff !important;
  }

  .ant-table-thead .ant-checkbox-wrapper:hover .ant-checkbox-inner {
    border-color: #fff !important;
  }

  .ant-table-thead .ant-checkbox-wrapper .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #fff !important;
    border-color: #fff !important;
  }

  .ant-table-thead .ant-checkbox-wrapper .ant-checkbox-checked .ant-checkbox-inner::after {
    border-color: #96bf49 !important;
  }
  
  .ant-checkbox+span {
    padding-inline-end: 0;
    padding-left: 8px;
  }

  /* Tab performance optimization */
  &__tabs {
    transition: opacity 0.15s ease-in-out;

    &.tab-changing {
      opacity: 0.7;
      pointer-events: none;

      .ant-tabs-content-holder {
        filter: blur(1px);
      }
    }
  }
  .ant-tabs-nav {
    margin-bottom: 10px !important;
  }

  // Tăng chiều cao cho empty rows
  .empty-row {
    height: 35px !important; // Tăng từ mặc định (~25px) lên 35px
  }

  // Đảm bảo empty rows có chiều cao tối thiểu
  .table-benh-vien-whitelist .ant-table-tbody > tr[data-row-key*="empty"],
  .table-benh-vien-blacklist .ant-table-tbody > tr[data-row-key*="empty"] {
    height: 35px !important;
    min-height: 35px !important;
  }

  // Tăng chiều cao cho tất cả cells trong empty rows
  .table-benh-vien-whitelist .ant-table-tbody > tr[data-row-key*="empty"] > td,
  .table-benh-vien-blacklist .ant-table-tbody > tr[data-row-key*="empty"] > td {
    height: 35px !important;
    min-height: 35px !important;
    padding: 8px 16px !important; // Tăng padding để tạo không gian
  }
}
