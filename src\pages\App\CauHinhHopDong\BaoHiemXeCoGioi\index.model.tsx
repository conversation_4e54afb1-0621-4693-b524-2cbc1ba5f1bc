import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface BaoHiemXeCoGioiContextProps {
  danhSachHopDongBaoHiemXeCoGioi: Array<CommonExecute.Execute.IHopDongXe>;
  danhSachKhachHang: Array<CommonExecute.Execute.IKhachHang>;
  loading: boolean;
  danhSachLichSuBoiThuong: Array<CommonExecute.Execute.ILichSuBoiThuongHopDong>;
  tongSoDong: number;
  danhSachDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  defaultFormValue: object;
  chiTietLichSuBoiThuong: CommonExecute.Execute.ILichSuBoiThuongHopDong | undefined;
  danhSachDoiTuongBaoHiemXe: Array<CommonExecute.Execute.IHopDongXe>;
  tongSoDongDoiTuongBaoHiemXe: number;
  tongPhiBaoHiemXeFromAPI: number;
  tongSoDongCanBoQuanLy: number;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listPhuongThucKhaiThac: Array<CommonExecute.Execute.IChiTietPhuongThucKhaiThac>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  listPhongBan: Array<CommonExecute.Execute.IPhongBan>;
  listDonViBoiThuong: Array<CommonExecute.Execute.INhaBaoHiemTPA>;
  listChuongTrinhBaoHiem: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
  listCanBo: Array<CommonExecute.Execute.IDoiTacNhanVien>;
  listLoaiXe: Array<CommonExecute.Execute.ILoaiXe>;
  listHangXe: Array<CommonExecute.Execute.IHangXe>;
  listHieuXe: Array<CommonExecute.Execute.IHieuXe>;
  listLoaiHinhNghiepVuXeCoGioi: Array<CommonExecute.Execute.ILoaiHinhNghiepVuXeCoGioi>;
  listDieuKhoanBoSungXeCoGioi: Array<CommonExecute.Execute.ILoaiHinhNghiepVuXeCoGioi>;
  listThongTinThanhToanHopDongXe: Array<CommonExecute.Execute.IThongTinThanhToanCuaHopDong>;
  tongSoDongDataKhachHang: number;
  tongSoDongDataLDaiLy: number;
  chiTietHopDongBaoHiemXe: CommonExecute.Execute.IHopDongXe;
  listDongBaoHiemHopDongXe: Array<CommonExecute.Execute.IDongBaoHiem>;
  listMucDoTonThatXe: Array<CommonExecute.Execute.IMucDoTonThatXe>;
  listHangMucXeTheoNhom: Array<CommonExecute.Execute.IHangMucXe>;
  listTaiBaoHiemHopDongXe: Array<CommonExecute.Execute.ITaiBaoHiem>;
  listDonViDongTai: Array<CommonExecute.Execute.IDonViDongTai>;
  danhSachHangMucTonThat: Array<any>;
  tongSoDongHangMucTonThat: number;
  chiTietDoiTuongBaoHiemXe: CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi;
  dataHangMucTonThatTheoDoiTuong: CommonExecute.Execute.IHangMucXe[];
  layDanhSachLichSuBoiThuongHopDong: (params: ReactQuery.ILayDanhSachLichSuBoiThuongHopDongParams) => Promise<any>;
  layChiTietLichSuBoiThuongHopDong: (params: ReactQuery.ILayChiTietLichSuBoiThuongHopDongParams) => Promise<any>;
  XoaLichSuBoiThuong: (params: ReactQuery.ILayChiTietLichSuBoiThuongHopDongParams) => Promise<any>;
  capNhatLichSuBoiThuong: (params: ReactQuery.ICapNhatLichSuBoiThuongHopDongParams) => Promise<any>;
  layDanhSachHopDongBaoHiemXePhanTrang: (params: ReactQuery.ILayDanhSachHopDongXePhanTrangParams) => void;
  timKiemPhanTrangKhachHang: (params: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => void;
  layChiTietHopDongBaoHiemXe: (params: ReactQuery.IChiTietHopDongXeParams) => Promise<CommonExecute.Execute.IHopDongXe | null>;
  getListCanBoQuanLy: (params: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams) => Promise<{data: Array<CommonExecute.Execute.IDoiTacNhanVien>; tong_so_dong: number}>;
  getListDaiLyKhaiThac: (params: ReactQuery.ILietKeDanhSachDaiLyParams) => Promise<{data: Array<CommonExecute.Execute.IDanhMucDaiLy>}>;
  searchKhachHang: (params: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => Promise<{tong_so_dong: number; data: Array<CommonExecute.Execute.IKhachHang>}>;
  timKiemPhanTrangDaiLy: (params: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams) => void;
  updateHopDongXe: (params: ReactQuery.IUpdateHopDongParams) => Promise<{
    success: boolean;
    isNewContract: boolean;
    contractInfo?: {
      so_id: number;
      so_hd: string;
    };
  }>;
  updateDoiTuongBaoHiemXe: (params: ReactQuery.IFormCapNhatDoiTuongBaoHiemXeCoGioiParams) => Promise<boolean>;
  timKiemPhanTrangDoiTuongBaoHiemXe: (params: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams) => void;
  layChiTietDoiTuongBaoHiemXe: (params: ReactQuery.IChiTietDoiTuongBaoHiemXeParams) => Promise<CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi | null>;
  layThongTinThanhToanCuaHopDongBaoHiemXe: (params: ReactQuery.ILayThongTinThanhToanCuaHopDongBaoHiemParams) => void;
  updateKyThanhToan: (params: ReactQuery.IUpdateKyThanhToanParams) => Promise<boolean>;
  layChiTietKyThanhToan: (params: ReactQuery.IChiTietKyThanhToanParams) => Promise<CommonExecute.Execute.IKyThanhToan | null>;
  layDanhSachCauHinhDongCuaHopDongBaoHiemXe: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise<any>;
  updateCauHinhDongBaoHiem: (params: ReactQuery.IUpdateCauHinhDongBHParams) => Promise<boolean>;
  layChiTietThongTinCauHinhDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<CommonExecute.Execute.IDongBaoHiem | null>;
  xoaThongTinDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<boolean>;
  layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise<any>;
  updateCauHinhTaiBaoHiem: (params: ReactQuery.IUpdateCauHinhTaiBHParams) => Promise<boolean>;
  xoaThongTinTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<boolean>;
  layChiTietThongTinCauHinhTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<CommonExecute.Execute.ITaiBaoHiem | null>;
  updateDoiTuongApDungTyLeDongBH: (params: ReactQuery.IUpdateDoiTuongApDungDongBHParams) => Promise<boolean>;
  updateDoiTuongApDungTaiBH: (params: ReactQuery.IUpdateDoiTuongApDungTaiBHParams) => Promise<boolean>;
  lietKeDanhSachCacDoiTuongDaDuocApDungDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<any>;
  lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<any>;
  huyHopDongXe: () => Promise<boolean>;
  goHuyHopDongXe: () => Promise<boolean>;
  getDanhSachFileThumbnailTheoDoiTuong: (params: ReactQuery.IGetFileThumbnailParams) => Promise<any>;
  uploadFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise<boolean>;
  deleteFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise<boolean>;
  phanLoaiFileTheoHangMucXe: (params: ReactQuery.IPhanLoaiFileTheoHangMucXeParams) => Promise<boolean>;
  getListHangMucXeTheoNhom: (params: ReactQuery.ILietKeHangMucXeTheoNhomParams) => Promise<any>;
  exportPdfHopDong: (params: ReactQuery.IExportPDFHopDongParams) => Promise<string | null>;
  exportExcel: (params: ReactQuery.IExportExcelParams) => Promise<string | null>;
  getListMucDoTonThatXe: () => Promise<any>;
  layDanhSachHangMucTonThatTheoDoiTuongXe: (params: ReactQuery.IChiTietDoiTuongBaoHiemXeParams) => Promise<any>;
  timKiemPhanTrangHangMucTonThat: (params: ReactQuery.ITimKiemPhanTrangHangMucTonThatParams) => Promise<any>;
  luuHangMucTonThat: (params: ReactQuery.ILuuHangMucTonThatParams) => Promise<boolean>;
  luuDanhGiaTonThatXe: (params: ReactQuery.ILuuDanhGiaTonThatParams) => Promise<boolean>;
  taoHopDongSuaDoiBoSung: (params: ReactQuery.ITaoHopDongSuaDoiBoSungParams) => Promise<{so_id: number; so_hd: string} | null>;
  resetChiTietHopDongBaoHiemXe: () => void;
}
