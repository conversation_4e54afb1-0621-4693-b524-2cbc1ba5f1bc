import {ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps, TabsProps} from "antd";

/* MODAL CẤU HÌNH MÃ BỆNH */

export const PAGE_SIZE = 10; // Số dòng mỗi trang
export interface IModalCauHinhMaBenhRef {
  open: (goiBaoHiemId?: number) => void;
  close: () => void;
}

export interface ModalCauHinhMaBenhProps {}

/* INTERFACES CHO API - XAY DUNG GOI BAO HIEM */
export interface ITimKiemMaBenhParams extends ReactQuery.IPhanTrang {
  id: number; // ID gói bảo hiểm
  loai_ad: "WL" | "BL"; // Loại áp dụng: WL = Whitelist, BL = Blacklist
  ten?: string; // Tên mã bệnh để tìm kiếm
  actionCode: string;
}

export interface ILayDanhSachMaBenhDaLuuParams {
  so_id: number; // ID hợp đồng
  so_id_dt: number; // ID đối tượng được bảo hiểm
  loai_ad: "WL" | "BL"; // Loại áp dụng
  actionCode: string;
}

export interface ILuuCauHinhMaBenhParams {
  so_id: number; // ID hợp đồng
  so_id_dt: number; // ID đối tượng được bảo hiểm
  loai_ad: "WL" | "BL"; // Loại áp dụng
  bvien: Array<{
    ma: string; // Mã bệnh
    hinh_thuc_ad: string; // Hình thức áp dụng
  }>;
  actionCode: string;
}

/* INTERFACES CHO DỮ LIỆU MÃ BỆNH */
export interface IMaBenh {
  id?: number;
  ma?: string;
  ten?: string;
  dia_chi?: string;
  dien_thoai?: string;
  email?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
  is_selected?: boolean; // Để track checkbox selection
  hinh_thuc_ap_dung?: string; // Hình thức áp dụng
}

/* TABLE DATA TYPES */
export interface TableMaBenhDataType {
  key: string;
  stt?: number;
  id?: number;
  ma?: string;
  ten?: string;
  dia_chi?: string;
  dien_thoai?: string;
  email?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
  is_selected?: boolean;
  hinh_thuc_ap_dung?: string;
}

/* CONSTANTS CHO HÌNH THỨC ÁP DỤNG */
export const HINH_THUC_AP_DUNG_OPTIONS = [
  {ten: "Áp dụng toàn bộ", ma: ""},
  {ten: "Áp dụng cho hồ sơ bảo lãnh", ma: "BL"},
  {ten: "Áp dụng cho hồ sơ trực tiếp", ma: "TT"},
];

/* CONSTANTS CHO LOẠI ÁP DỤNG */
export const LOAI_AP_DUNG = {
  WHITELIST: "WL" as const,
  BLACKLIST: "BL" as const,
};

/* TAB CONFIGURATION */
export const tabsCauHinhMaBenh: TabsProps["items"] = [
  {
    key: "1",
    label: "Mã bệnh white list",
  },
  {
    key: "2",
    label: "Mã bệnh black list",
  },
];

/* COLUMN DEFINITIONS */
export const maBenhColumns: TableProps<TableMaBenhDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: colWidthByKey.sott,
    align: "center",
    render: (_: any, record: TableMaBenhDataType, index: number) => {
      if (record.key.includes("empty")) return "\u00A0";
      return index + 1;
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Chọn",
    dataIndex: "is_selected",
    key: "is_selected",
    width: 80,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã bệnh",
    dataIndex: "ma",
    key: "ma",
    width: 160,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên mã bệnh",
    dataIndex: "ten",
    key: "ten",
    align: "left",
  },
  {
    ...defaultTableColumnsProps,
    title: "Hình thức áp dụng",
    dataIndex: "hinh_thuc_ap_dung",
    key: "hinh_thuc_ap_dung",
    width: 250,
    align: "center",
  },
];

/* REF INTERFACES CHO TABLE COMPONENTS */
export interface ITableMaBenhWhitelistRef {
  getData: () => TableMaBenhDataType[]; // Trả về tất cả items được chọn từ globalSelections
  setData: (data: TableMaBenhDataType[]) => void;
  refreshData: () => void; // Refresh data sau khi lưu
  resetSelections: () => void; // Reset tất cả selections tạm thời, giữ nguyên data từ API
}

export interface ITableMaBenhBlacklistRef {
  getData: () => TableMaBenhDataType[]; // Trả về tất cả items được chọn từ globalSelections
  setData: (data: TableMaBenhDataType[]) => void;
  refreshData: () => void; // Refresh data sau khi lưu
  resetSelections: () => void; // Reset tất cả selections tạm thời, giữ nguyên data từ API
}

/* PROPS CHO TABLE COMPONENTS */
export interface TableMaBenhProps {
  goiBaoHiemId?: number; // ID gói bảo hiểm
  loaiApDung: "WL" | "BL";
  onDataChange?: (data: TableMaBenhDataType[]) => void;
}
