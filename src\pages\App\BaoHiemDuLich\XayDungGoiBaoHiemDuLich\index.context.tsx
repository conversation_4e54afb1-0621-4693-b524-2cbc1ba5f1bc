import {createContext, useContext} from "react";

import {IXayDungGoiBaoHiemContextProps} from "./index.model";
import {ReactQuery} from "@src/@types";

export const XayDungGoiBaoHiemContext = createContext<IXayDungGoiBaoHiemContextProps>({
  windowHeight: 0,

  loading: false,
  filterGoiBaoHiemParams: {},
  tongSoDongHopDong: 0,
  listGoiBaoHiem: [],
  listQuyenLoiRoot: [],

  listDoiTac: [],
  listSanPham: [],
  listNguyenTe: [],

  timKiemPhanTrangGoiBaoHiem: () => Promise.resolve(),
  setFilterGoiBaoHiemParams: () => {},
  getChiTietGoiBaoHiem: (params: ReactQuery.IChiTietGoiBaoHiemParams) => Promise.resolve({} as CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi),
  timKiemPhanTrangBoMaQuyenLoi: (params: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams) => {},
  updateGoiBaoHiem: (params: ReactQuery.ICapNhatGoiBaoHiemParams) => Promise.resolve({}),

  // Bệnh viện API functions
  timKiemBenhVienPhanTrang: (params: ReactQuery.ITimKiemBenhVienParams) => Promise.resolve({}),
  layDanhSachBenhVienDaLuu: (params: ReactQuery.ILayDanhSachBenhVienDaLuuParams) => Promise.resolve({}),
  luuCauHinhBenhVien: (params: ReactQuery.ILuuCauHinhBenhVienParams) => Promise.resolve({}),

  timKiemPhanTrangMaBenhGoiBH: (params: ReactQuery.ITimKiemMaBenhGoiBaoHiemParams) => Promise.resolve({}),
  layDanhSachMaBenhDaLuuGoiBaoHiem: (params: ReactQuery.ILayDanhSachMaBenhDaLuuGoiBaoHiemParams) => Promise.resolve({}),
  luuCauHinhMaBenhGoiBaoHiem: (params: ReactQuery.ILuuCauHinhMaBenhGoiBaoHiemParams) => Promise.resolve(false),
} as any);

export const useXayDungGoiBaoHiemContext = () => useContext(XayDungGoiBaoHiemContext);
