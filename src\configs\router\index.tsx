import loadable from "@loadable/component";
import {PathProps} from "@src/@types";
import Main from "@src/components/Main";
import {ROUTER_PATHS} from "@src/constants";
import {ThemeMode, useProfile, useSetting} from "@src/hooks";
import {useMenuNguoiDung} from "@src/hooks/menuNguoiDungStore";
import {MessageProvider, NotiProvider} from "@src/providers";
import {generateRouter, setInfoToCookies, setNavigate, toggleDarkMode} from "@src/utils";
import {getDanhMucChucDanh, getDanhMucPhongBan, getDanhMucTinhThanh, getListChiNhanh, getListDoiTac, getListhMenuNguoiDungTheoNhom} from "@src/utils/initData";
import React, {ComponentType, useCallback, useEffect, useMemo} from "react";
import {Routes, useNavigate} from "react-router-dom";
import i18n from "../i18n";

export const BO_MA_MENU = {
  // ADMIN
  DASHBOARD: "MN2025010100000001", //dashboard
  QUAN_TRI_HE_THONG: "MN2024080000000043", //quản trị hệ thống
  DANH_MUC_CHUNG: "MN2024080000000002", //danh mục chung
  QUAN_LY_DOI_TAC: "MN2024080000000003", //quản lý đối tác
  QUAN_LY_DON_VI_CHI_NHANH: "MN2024080000000004", //quản lý đơn vị chi nhánh
  QUAN_LY_PHONG_BAN: "MN2024080000000005", //quản lý phòng ban
  QUAN_LY_CHUC_DANH: "MN2024080000000006", //quản lý chức danh
  DANH_MUC_TINH_THANH: "MN2024080000000021", //danh mục tỉnh thành
  DANH_MUC_QUAN_HUYEN: "MN2024080000000022", //danh mục quận huyện
  DANH_MUC_PHUONG_XA: "MN2024080000000023", //danh mục xã phường
  DANH_MUC_CHAU_LUC: "MN2025071600000242", //danh mục châu
  DANH_MUC_KHU_VUC: "MN2025071600000243", //danh mục khu vực
  DANH_MUC_QUOC_GIA: "MN2025071600000244", //danh mục quốc gia
  DANH_MUC_BENH_VIEN: "MN2025072300000261", //danh mục bệnh viện
  DANH_MUC_BANG_MA_BENH: "MN2025081800000361", //danh mục bảng mã bệnh
  DANH_MUC_NHOM_MA_BENH: "MN2025081800000363", //danh mục nhóm mã bệnh
  DANH_MUC_MA_BENH_ICD: "MN2025081800000362", //danh mục mã bệnh ICD
  DANH_MUC_NGAN_HANG: "MN2024080000000024", //danh mục ngân hàng
  LOAI_HO_GIA_DINH: "MN2025082700000384", //danh mục loại hộ gia đình bhxh
  DON_VI_THU_HO: "MN2025090300000402", //danh mục đơn vị thu hộ BHXH
  TAI_KHOAN_DON_VI_THU_HO: "MN2025090300000403", //danh mục tài khoản đơn vị thu hộ BHXH
  DANH_MUC_CHI_NHANH_NGAN_HANG: "MN2024080000000025", //danh mục chi nhánh ngân hàng
  DANH_MUC_HOP_DONG: "MN2024080000000007", //danh mục hợp đồng
  PHUONG_THUC_KHAI_THAC: "MN2024080000000008", //phương thức khai thác
  DANH_MUC_DAI_LY: "MN2024080000000009", //danh mục đại lý
  DANH_MUC_SAN_PHAM: "MN2024080000000010", //danh mục sản phảm
  BO_MA_QUYEN_LOI: "MN2024080000000011", //BỘ MÃ QUYỀN LỢI
  BO_MA_NGUYEN_TE: "MN2024080000000012", //BỘ MÃ NGUYÊN TỆ
  DANH_MUC_NHOM_DOI_TUONG: "MN2025030300000121", //DANH MỤC NHÓM ĐỐI TƯỢNG
  CAU_HINH_HE_THONG: "MN2024080000000018", //CẤU HÌNH HỆ THỐNG
  BO_MA_CAU_HOI: "MN2025071600000241", //CẤU HÌNH BỘ MÃ CÂU HỎI
  DANH_MUC_NGHIEP_VU: "MN2025080500000281", //DANH MỤC NGHIỆP VỤ
  CAU_HINH_MAU_HOP_DONG: "MN2025070700000201", //CẤU HÌNH MẪU HỢP ĐỒNG
  CAU_HINH_MAU_GCN: "MN2025071700000245",
  HE_THONG_MENU: "MN2024080000000019", //HỆ THỐNG MENU
  HE_THONG_CHUC_NANG: "MN2024080000000063", //HỆ THỐNG CHỨC NĂNG
  PHAN_NHOM_CHUC_NANG: "MN2024110400000081", //PHÂN NHÓM CHỨC NĂNG
  PHAN_CHUC_NANG_THEO_VAI_TRO: "MN2024080000000062", //PHÂN CHỨC NĂNG THEO VAI TRÒ
  CAU_HINH_TY_LE_HOA_HONG: "MN2025072300000262", //CẤU HÌNH TỶ LỆ HOA HỒNG
  HE_THONG_TAI_KHOAN: "MN2024080000000020", //HỆ THỐNG TÀI KHOẢN
  TAI_KHOAN_NGUOI_DUNG: "MN2024080000000026", //TÀI KHOẢN NGƯỜI DÙNG
  DANH_MUC_HANG_XE: "MN2024080000000089", //DANH MỤC HÃNG XE
  DANH_MUC_HIEU_XE: "MN2024080000000090", //DANH MỤC HIỆU XE
  DANH_MUC_LOAI_XE: "MN2024080000000091", //DANH MỤC LOẠI XE
  NHOM_HANG_MUC_XE: "MN2025070400000181", //Nhóm hạng mục xe
  HANG_MUC_XE: "MN2025070400000182", //Hạng mục xe
  MUC_DO_TON_THAT_XE: "MN2025070900000221", //Mức độ tồn thất xe
  CAU_HINH_PHAN_CAP_PHE_DUYET: "MN2025070800000202", //cấu hình phân cấp phê duyệt
  NHOM_PHAN_CAP_DUYET: "MN2025070800000203", //nhóm phân cấp duyệt
  NHOM_DOI_TUONG: "MN2025080800000321", //nhóm đối tượng
  CAU_HINH_NGAN_SACH_HO_TRO: "MN2025082700000385", //cấu hình ngân sách hỗ trợ
  DANH_SACH_DON_VI_BHXH: "MN2025090300000401", //đơn vị bhxh
  CAU_HINH_HUONG_HOA_HONG: "MN2025082700000386", //cấu hình hưởng hoa hồng
  TAI_KHOAN_DAI_LY: "MN2025092900000462", //tài khoản đại lý
  // CLIENT
  BAO_HIEM_CON_NGUOI: "MN2024080000000038",
  HOP_DONG_CON_NGUOI: "MN2024080000000040",
  XAY_DUNG_GOI_BAO_HIEM: "MN2024080000000041",
  CHUONG_TRINH_BAO_HIEM: "MN2024080000000061",
  XAY_DUNG_GOI_BAO_HIEM_DU_LICH: "MN2025100600000484",

  QUAN_LY_THONG_TIN_KHACH_HANG: "MN2024080000000042",
  TIEP_NHAN_HO_SO: "MN2024080000000039",
  BAO_HIEM_XE_CO_GIOI: "MN2025032200000141",
  HE_THONG_BAO_CAO: "MN2024080000000033",
  BAO_CAO_HO_SO_PHAT_SINH: "MN2024080000000034",
  BAO_CAO_HSBT_CHUA_GIAI_QUYET: "MN2024080000000035",
  BAO_CAO_HSBT_DA_GIAI_QUYET: "MN2024080000000036",
  BAO_CAO_DU_PHONG: "MN2024080000000037",
  PHE_DUYET_HD_XCG: "MN2025070200000162",
  PHE_DUYET_HD_CON_NGUOI: "MN2025081500000341",
  BAO_HIEM_TAI_SAN: "MN2025080700000301",
};

/**
 * Lazy load:
 * loadable(): Đây là một hàm từ thư viện @loadable/component (hoặc react-loadable),
 *  dùng để thực hiện lazy loading cho component trong React.
 *  Khi bạn sử dụng loadable(), React sẽ chỉ tải component khi cần thiết (ví dụ: khi nó được render lần đầu tiên).
 *  Điều này giúp tối ưu hóa hiệu suất bằng cách giảm bớt tải tài nguyên không cần thiết lúc đầu.
 *  sử dụng loadable() để tải component QuanLyChucDanh khi cần thiết,
 *  thay vì tải nó ngay từ đầu, giúp tối ưu hóa thời gian tải trang.
 */
// AUTH
const DangNhapLazy = loadable(() => import("@pages/Auth/DangNhap"));

// APP
const TrangChuLazy = loadable(() => import("@pages/App/TrangChu"));
const DashboardLazy = loadable(() => import("@pages/App/Dashboard"));
const NotFoundPageLazy = loadable(() => import("@pages/App/NotFoundPage"));

// DANH MỤC CHUNG
const QuanLyDoiTacLazy = loadable(() => import("@pages/App/DanhMucChung/QuanLyDoiTac")); // Quản lý đối tác
const DanhSachPhongBanLazy = loadable(() => import("@src/pages/App/DanhMucChung/QuanLyPhongBan")); //Quản lý Phòng ban
const QuanLyDonViChiNhanhLazy = loadable(() => import("@src/pages/App/DanhMucChung/QuanLyDonViChiNhanh")); //Quản lý Phòng ban
const QuanLyChucDanhLazy = loadable(() => import("@src/pages/App/DanhMucChung/QuanLyChucDanh")); //Quản lý Chức danh
const QuanLyTinhThanhLazy = loadable(() => import("@src/pages/App/DanhMucChung/QuanLyTinhThanh")); //Quản lý tỉnh thành
const DanhMucQuanHuyenLazy = loadable(() => import("@src/pages/App/DanhMucChung/DanhMucQuanHuyen")); //Quản lý Quận huyện
const DanhMucPhuongXaLazy = loadable(() => import("@src/pages/App/DanhMucChung/DanhMucPhuongXa")); //Quản lý Phường xã
const DanhMucNganHangLazy = loadable(() => import("@src/pages/App/DanhMucChung/DanhMucNganHang")); //Quản lý Chức danh
const DanhMucChiNhanhNganHangLazy = loadable(() => import("@src/pages/App/DanhMucChung/DanhMucChiNhanhNganHang"));
const DanhMucChauLucLazy = loadable(() => import("@src/pages/App/DanhMucChung/DanhMucChauLuc")); //Quản lý Châu lục
const DanhMucKhuVucLazy = loadable(() => import("@src/pages/App/DanhMucChung/DanhMucKhuVuc")); //Quản lý Khu vực
const DanhMucQuocGiaLazy = loadable(() => import("@src/pages/App/DanhMucChung/DanhMucQuocGia")); //Quản lý Quốc gia
const DanhMucBenhVienLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/DanhMucBenhVien")); //Quản lý bệnh viện
const DanhMucBangMaBenhLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/DanhMucBangMaBenh"));
const DanhMucNhomMaBenhLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/DanhMucNhomMaBenh"));
const DanhMucMaBenhICDLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/DanhMucMaBenhICD"));
// HỆ THỐNG TÀI KHOẢN
const QuanLyTaiKhoanNguoiDungLazy = loadable(() => import("@src/pages/App/HeThongTaiKhoan/QuanLyTaiKhoanNguoiDung")); //Quản lý Phòng ban
const TaiKhoanDaiLyLazy = loadable(() => import("@src/pages/App/HeThongTaiKhoan/TaiKhoanDaiLy")); //Quản lý tài khoản đại lý
const CauHinhPhanCapPheDuyetLazy = loadable(() => import("@src/pages/App/HeThongTaiKhoan/CauHinhPhanCapPheDuyet")); //cấu hình phân cấp phê duyệt
// CẤU HÌNH HỆ THỐNG
const HeThongMenuLazy = loadable(() => import("@src/pages/App/CauHinhHeThong/HeThongMenu")); //Quản lý hệ thống menu
const HeThongChucNangLazy = loadable(() => import("@src/pages/App/CauHinhHeThong/HeThongChucNang")); //Quản lý Phòng ban
const NhomChucNangLazy = loadable(() => import("@src/pages/App/CauHinhHeThong/NhomChucNang")); //Quản lý Phòng ban
const CauHinhMauHopDongLazy = loadable(() => import("@src/pages/App/CauHinhHeThong/CauHinhMauHopDong")); //Cấu hình mẫu hợp đồng
const CauHinhMauGCNLazy = loadable(() => import("@src/pages/App/CauHinhHeThong/CauHinhMauGCN")); //Cấu hình mẫu giấy chứng nhận
const NhomPhanCapDuyetLazy = loadable(() => import("@src/pages/App/CauHinhHeThong/NhomPhanCapDuyet")); //Cấu hình nhóm phân cấp duyệt
const CauHinhTyLeHoaHongLazy = loadable(() => import("@src/pages/App/CauHinhHeThong/CauHinhTyLeHoaHong")); //Cấu hình tỷ lệ hoa hồng
const ChucNangTheoVaiTroLayzy = loadable(() => import("@src/pages/App/CauHinhHeThong/ChucNangTheoVaiTro")); //Quản lý chức năng theo vai trò
// BẢO HIỂM CON NGƯỜI
const HopDongConNguoiLazy = loadable(() => import("@src/pages/App/BaoHiemConNguoi/HopDongConNguoi")); //Quản lý Phòng ban
const ChuongTrinhBaoHiemLazy = loadable(() => import("@src/pages/App/BaoHiemConNguoi/ChuongTrinhBaoHiem")); //Quản lý chương trình bảo hiểm
const XayDungGoiBaoHiemLazy = loadable(() => import("@src/pages/App/BaoHiemConNguoi/XayDungGoiBaoHiem")); //Quản lý Phòng ban
//BẢO HIỂM DU LỊCH
const XayDungGoiBaoHiemDuLichLazy = loadable(() => import("@src/pages/App/BaoHiemDuLich/XayDungGoiBaoHiemDuLich")); //Quản lý xây dựng gói bảo hiểm du lịch
// QUẢN LÝ KHÁCH HÀNG
const QuanLyKhachHangLazy = loadable(() => import("@src/pages/App/QuanLyKhachHang")); //Quản lý khách hàng
//BẢO HIỂM XÃ HỘI
const CauHinhNganSachHoTroLazy = loadable(() => import("@src/pages/App/BaoHiemXaHoi/CauHinhNganSachHoTro")); //Quản lý ngân sách hỗ trợ
const DanhSachDonViBHXHLazy = loadable(() => import("@src/pages/App/BaoHiemXaHoi/DanhSachDonViBHXH")); //Quản lý đơn vị BHXH
const LoaiHoGiaDinhLazy = loadable(() => import("@src/pages/App/BaoHiemXaHoi/LoaiHoGiaDinh"));
const DonViThuHoLazy = loadable(() => import("@src/pages/App/BaoHiemXaHoi/DonViThuHo"));
const TaiKhoanDonViThuHoLazy = loadable(() => import("@src/pages/App/BaoHiemXaHoi/TaiKhoanDonViThuHo")); //Tài khoản đơn vị thu hộ
const CauHinhHuongHoaHongLazy = loadable(() => import("@src/pages/App/BaoHiemXaHoi/CauHinhHuongHoaHong")); //Cấu hình hướng hoa hồng
//CẤU HÌNH HỢP ĐỒNG
const HopDongBaoHiemXeCoGioiLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/BaoHiemXeCoGioi")); //Quản lý Phòng ban
const DanhMucDaiLyLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/DanhMucDaiLy")); //Quản lý danh mục đại lý
const PhuongThucKhaiThacLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/PhuongThucKhaiThac")); //quản lý phương thúc khai thác
const DanhMucSanPhamLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/DanhMucSanPham")); //Danh mục sản phẩm
const BoMaNguyenTeLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/BoMaNguyenTe")); //Quản lý bộ mã nguyện tệ
const BoMaQuyenLoiLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/BoMaQuyenLoi")); //Quản lý bộ mã quyền lợi
const BoMaCauHoiLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/CauHinhBoMaCauHoi")); //Quản lý bộ mã câu hỏi
const DanhMucNghiepVuLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/DanhMucNghiepVu")); //Quản lý danh mục nghiệp vụ
const NhomDoiTuongLazy = loadable(() => import("@src/pages/App/CauHinhHopDong/NhomDoiTuong")); //Quản lý nhóm đối tượng
//DANH MỤC HỢP ĐỒNG XE CƠ GIỚI
const DanhMuchangXeLazy = loadable(() => import("@src/pages/App/DanhMucHDXeCoGioi/DanhMucHangXe")); //Quản lý danh mục hãng xe
const DanhMucHieuXeLazy = loadable(() => import("@src/pages/App/DanhMucHDXeCoGioi/DanhMucHieuXe")); //Quản lý danh mục hiệu xe
const DanhMucLoaiXeLazy = loadable(() => import("@src/pages/App/DanhMucHDXeCoGioi/DanhMucLoaiXe")); //Quản lý danh mục loại xe
const NhomHangMucXeLazy = loadable(() => import("@src/pages/App/DanhMucHDXeCoGioi/NhomHangMucXe")); //Quản lý danh mục loại xe
const HangMucXeLazy = loadable(() => import("@src/pages/App/DanhMucHDXeCoGioi/HangMucXe")); //Quản lý danh mục loại xe
const MucDoTonThatXeLazy = loadable(() => import("@src/pages/App/DanhMucHDXeCoGioi/MucDoTonThatXe")); //Quản lý mức độ tổn thất xe
//BẢO HIỂM TÀI SẢN
const BaoHiemTaiSanLazy = loadable(() => import("@src/pages/App/BaoHiemTaiSan")); //Quản lý danh mục loại xe
//PHÊ DUYỆT HĐ
const PheDuyetHopDongXCGLazy = loadable(() => import("@src/pages/App/PheDuyetHopDong/PheDuyetHopDongXCG")); //Phê duyệt hợp đồng xe cơ giới
const PheDuyetHopDongConNguoiLazy = loadable(() => import("@src/pages/App/PheDuyetHopDong/PheDuyetHopDongConNguoi")); //Phê duyệt hợp đồng con người
//CẤU HÌNH HỢP ĐỒNG
/**
 * as unknown as ComponentType<any>: Đây là cách sử dụng type casting trong TypeScript.
 *  Cụ thể:
- QuanLyChucDanhLazy sau khi được lazy load sẽ có kiểu LoadableComponent từ thư viện @loadable/component.
 Tuy nhiên, trong React, bạn thường làm việc với các component có kiểu React.ComponentType, hoặc JSX.Element.
- as unknown as ComponentType<any> là cách ép kiểu từ LoadableComponent (hoặc kiểu khác) thành kiểu ComponentType<any>,
 nghĩa là chuyển QuanLyChucDanhLazy về kiểu một component React thông thường.
- unknown là một kiểu an toàn trong TypeScript, nghĩa là bạn không thể thao tác trực tiếp với giá trị này cho đến khi
 bạn ép kiểu nó thành kiểu cụ thể. Việc ép kiểu thành ComponentType<any> sau đó cho phép bạn sử dụng QuanLyChucDanh như một
  component React trong ứng dụng của mình.
  Type casting:  ép kiểu QuanLyChucDanhLazy (một component lazy-loaded) thành kiểu ComponentType<any>,
   đảm bảo nó có thể được sử dụng như một component React thông thường trong TypeScript.
 */
const DangNhap = DangNhapLazy as unknown as ComponentType<any>;
const TrangChu = TrangChuLazy as unknown as ComponentType<any>;
const Dashboard = DashboardLazy as unknown as ComponentType<any>;
const NotFoundPage = NotFoundPageLazy as unknown as ComponentType<any>;
const QuanLyDoiTac = QuanLyDoiTacLazy as unknown as ComponentType<any>;
const DanhSachPhongBan = DanhSachPhongBanLazy as unknown as ComponentType<any>;
const QuanLyDonViChiNhanh = QuanLyDonViChiNhanhLazy as unknown as ComponentType<any>;
const QuanLyTaiKhoanNguoiDung = QuanLyTaiKhoanNguoiDungLazy as unknown as ComponentType<any>;
const HeThongChucNang = HeThongChucNangLazy as unknown as ComponentType<any>;
const QuanLyChucDanh = QuanLyChucDanhLazy as unknown as ComponentType<any>;
const NhomChucNang = NhomChucNangLazy as unknown as ComponentType<any>;
const QuanLyTinhThanh = QuanLyTinhThanhLazy as unknown as ComponentType<any>;
const DanhMucQuanHuyen = DanhMucQuanHuyenLazy as unknown as ComponentType<any>;
const DanhMucPhuongXa = DanhMucPhuongXaLazy as unknown as ComponentType<any>;
const DanhMucChauLuc = DanhMucChauLucLazy as unknown as ComponentType<any>;
const DanhMucKhuVuc = DanhMucKhuVucLazy as unknown as ComponentType<any>;
const DanhMucQuocGia = DanhMucQuocGiaLazy as unknown as ComponentType<any>;
const DanhMucBenhVien = DanhMucBenhVienLazy as unknown as ComponentType<any>;
const DanhMucBangMaBenh = DanhMucBangMaBenhLazy as unknown as ComponentType<any>;
const DanhMucNhomMaBenh = DanhMucNhomMaBenhLazy as unknown as ComponentType<any>;
const DanhMucMaBenhICD = DanhMucMaBenhICDLazy as unknown as ComponentType<any>;
const LoaiHoGiaDinh = LoaiHoGiaDinhLazy as unknown as ComponentType<any>;
const DonViThuHo = DonViThuHoLazy as unknown as ComponentType<any>;
const TaiKhoanDonViThuHo = TaiKhoanDonViThuHoLazy as unknown as ComponentType<any>;
const HopDongConNguoi = HopDongConNguoiLazy as unknown as ComponentType<any>;
const HopDongBaoHiemXeCoGioi = HopDongBaoHiemXeCoGioiLazy as unknown as ComponentType<any>;
const PhuongThucKhaiThac = PhuongThucKhaiThacLazy as unknown as ComponentType<any>;
const DanhMucDaiLy = DanhMucDaiLyLazy as unknown as ComponentType<any>;
const DanhMucSanPham = DanhMucSanPhamLazy as unknown as ComponentType<any>;
const BoMaNguyenTe = BoMaNguyenTeLazy as unknown as ComponentType<any>;
const BoMaQuyenLoi = BoMaQuyenLoiLazy as unknown as ComponentType<any>;
const HeThongMenu = HeThongMenuLazy as unknown as ComponentType<any>;
const DanhMucNganHang = DanhMucNganHangLazy as unknown as ComponentType<any>;
const DanhMucChiNhanhNganHang = DanhMucChiNhanhNganHangLazy as unknown as ComponentType<any>;
const ChuongTrinhBaoHiem = ChuongTrinhBaoHiemLazy as unknown as ComponentType<any>;
const XayDungGoiBaoHiem = XayDungGoiBaoHiemLazy as unknown as ComponentType<any>;
const QuanLyKhachHang = QuanLyKhachHangLazy as unknown as ComponentType<any>;
const DanhMucHangXe = DanhMuchangXeLazy as unknown as ComponentType<any>;
const DanhMucHieuXe = DanhMucHieuXeLazy as unknown as ComponentType<any>;
const DanhMucLoaiXe = DanhMucLoaiXeLazy as unknown as ComponentType<any>;
const NhomHangMucXe = NhomHangMucXeLazy as unknown as ComponentType<any>;
const HangMucXe = HangMucXeLazy as unknown as ComponentType<any>;
const MucDoTonThatXe = MucDoTonThatXeLazy as unknown as ComponentType<any>;
const PheDuyetHopDongXCG = PheDuyetHopDongXCGLazy as unknown as ComponentType<any>;
const PheDuyetHopDongConNguoi = PheDuyetHopDongConNguoiLazy as unknown as ComponentType<any>;
const CauHinhPhanCapPheDuyet = CauHinhPhanCapPheDuyetLazy as unknown as ComponentType<any>;
const CauHinhMauHopDong = CauHinhMauHopDongLazy as unknown as ComponentType<any>;
const CauHinhMauGCN = CauHinhMauGCNLazy as unknown as ComponentType<any>;
const BoMaCauHoi = BoMaCauHoiLazy as unknown as ComponentType<any>;
const NhomPhanCapDuyet = NhomPhanCapDuyetLazy as unknown as ComponentType<any>;
const CauHinhTyLeHoaHong = CauHinhTyLeHoaHongLazy as unknown as ComponentType<any>;
const ChucNangTheoVaiTro = ChucNangTheoVaiTroLayzy as unknown as ComponentType<any>;
const DanhMucNghiepVu = DanhMucNghiepVuLazy as unknown as ComponentType<any>;
const BaoHiemTaiSan = BaoHiemTaiSanLazy as unknown as ComponentType<any>;
const NhomDoiTuong = NhomDoiTuongLazy as unknown as ComponentType<any>;
const CauHinhNganSachHoTro = CauHinhNganSachHoTroLazy as unknown as ComponentType<any>;
const DanhSachDonViBHXH = DanhSachDonViBHXHLazy as unknown as ComponentType<any>;
const CauHinhHuongHoaHong = CauHinhHuongHoaHongLazy as unknown as ComponentType<any>;
const TaiKhoanDaiLy = TaiKhoanDaiLyLazy as unknown as ComponentType<any>;
const XayDungGoiBaoHiemDuLich = XayDungGoiBaoHiemDuLichLazy as unknown as ComponentType<any>;

export const Navigation: React.FC = () => {
  const navigate = useNavigate();
  const {profile} = useProfile();
  const {menuNguoiDung} = useMenuNguoiDung();
  const {lang, themeMode} = useSetting();

  //set tiện tích navigate để sử dụng ở cả file function bình thường, vì useNavigate cần sử dụng trong file component
  useEffect(() => {
    if (navigate) {
      setNavigate(navigate);
    }
  }, [navigate]);

  //lấy ra ScreenComponent theo mã menu
  const getScreenByMaMenu = useCallback((maMenu: string) => {
    switch (maMenu) {
      case BO_MA_MENU.DASHBOARD:
        return <Dashboard />;
      case BO_MA_MENU.QUAN_TRI_HE_THONG:
        return <TrangChu />;
      case BO_MA_MENU.QUAN_LY_DOI_TAC:
        return <QuanLyDoiTac />;
      case BO_MA_MENU.QUAN_LY_PHONG_BAN:
        return <DanhSachPhongBan />;
      case BO_MA_MENU.QUAN_LY_DON_VI_CHI_NHANH:
        return <QuanLyDonViChiNhanh />;
      case BO_MA_MENU.TAI_KHOAN_NGUOI_DUNG:
        return <QuanLyTaiKhoanNguoiDung />;
      case BO_MA_MENU.HE_THONG_CHUC_NANG:
        return <HeThongChucNang />;
      case BO_MA_MENU.QUAN_LY_CHUC_DANH:
        return <QuanLyChucDanh />;
      case BO_MA_MENU.PHAN_NHOM_CHUC_NANG:
        return <NhomChucNang />;
      case BO_MA_MENU.DANH_MUC_TINH_THANH:
        return <QuanLyTinhThanh />;
      case BO_MA_MENU.DANH_MUC_QUAN_HUYEN:
        return <DanhMucQuanHuyen />;
      case BO_MA_MENU.DANH_MUC_PHUONG_XA:
        return <DanhMucPhuongXa />;
      case BO_MA_MENU.DANH_MUC_CHAU_LUC:
        return <DanhMucChauLuc />;
      case BO_MA_MENU.DANH_MUC_KHU_VUC:
        return <DanhMucKhuVuc />;
      case BO_MA_MENU.DANH_MUC_QUOC_GIA:
        return <DanhMucQuocGia />;
      case BO_MA_MENU.DANH_MUC_BENH_VIEN:
        return <DanhMucBenhVien />;
      case BO_MA_MENU.DANH_MUC_BANG_MA_BENH:
        return <DanhMucBangMaBenh />;
      case BO_MA_MENU.DANH_MUC_NHOM_MA_BENH:
        return <DanhMucNhomMaBenh />;
      case BO_MA_MENU.DANH_MUC_MA_BENH_ICD:
        return <DanhMucMaBenhICD />;
      case BO_MA_MENU.LOAI_HO_GIA_DINH:
        return <LoaiHoGiaDinh />;
      case BO_MA_MENU.DON_VI_THU_HO:
        return <DonViThuHo />;
      case BO_MA_MENU.TAI_KHOAN_DON_VI_THU_HO:
        return <TaiKhoanDonViThuHo />;
      case BO_MA_MENU.HOP_DONG_CON_NGUOI:
        return <HopDongConNguoi />;
      case BO_MA_MENU.BAO_HIEM_XE_CO_GIOI:
        return <HopDongBaoHiemXeCoGioi />;
      case BO_MA_MENU.DANH_MUC_DAI_LY:
        return <DanhMucDaiLy />;
      case BO_MA_MENU.DANH_MUC_SAN_PHAM:
        return <DanhMucSanPham />;
      case BO_MA_MENU.PHUONG_THUC_KHAI_THAC:
        return <PhuongThucKhaiThac />;
      case BO_MA_MENU.BO_MA_NGUYEN_TE:
        return <BoMaNguyenTe />;
      case BO_MA_MENU.BO_MA_QUYEN_LOI:
        return <BoMaQuyenLoi />;
      case BO_MA_MENU.HE_THONG_MENU:
        return <HeThongMenu />;
      case BO_MA_MENU.DANH_MUC_NGAN_HANG:
        return <DanhMucNganHang />;
      case BO_MA_MENU.DANH_MUC_CHI_NHANH_NGAN_HANG:
        return <DanhMucChiNhanhNganHang />;
      case BO_MA_MENU.CHUONG_TRINH_BAO_HIEM:
        return <ChuongTrinhBaoHiem />;
      case BO_MA_MENU.XAY_DUNG_GOI_BAO_HIEM:
        return <XayDungGoiBaoHiem />;
      case BO_MA_MENU.QUAN_LY_THONG_TIN_KHACH_HANG:
        return <QuanLyKhachHang />;
      case BO_MA_MENU.DANH_MUC_HANG_XE:
        return <DanhMucHangXe />;
      case BO_MA_MENU.DANH_MUC_HIEU_XE:
        return <DanhMucHieuXe />;
      case BO_MA_MENU.DANH_MUC_LOAI_XE:
        return <DanhMucLoaiXe />;
      case BO_MA_MENU.NHOM_HANG_MUC_XE:
        return <NhomHangMucXe />;
      case BO_MA_MENU.HANG_MUC_XE:
        return <HangMucXe />;
      case BO_MA_MENU.MUC_DO_TON_THAT_XE:
        return <MucDoTonThatXe />;
      case BO_MA_MENU.PHE_DUYET_HD_XCG:
        return <PheDuyetHopDongXCG />;
      case BO_MA_MENU.PHE_DUYET_HD_CON_NGUOI:
        return <PheDuyetHopDongConNguoi />;
      case BO_MA_MENU.CAU_HINH_PHAN_CAP_PHE_DUYET:
        return <CauHinhPhanCapPheDuyet />;
      case BO_MA_MENU.CAU_HINH_MAU_HOP_DONG:
        return <CauHinhMauHopDong />;
      case BO_MA_MENU.CAU_HINH_MAU_GCN:
        return <CauHinhMauGCN />;
      case BO_MA_MENU.BO_MA_CAU_HOI:
        return <BoMaCauHoi />;
      case BO_MA_MENU.NHOM_PHAN_CAP_DUYET:
        return <NhomPhanCapDuyet />;
      case BO_MA_MENU.CAU_HINH_TY_LE_HOA_HONG:
        return <CauHinhTyLeHoaHong />;
      case BO_MA_MENU.PHAN_CHUC_NANG_THEO_VAI_TRO:
        return <ChucNangTheoVaiTro />;
      case BO_MA_MENU.DANH_MUC_NGHIEP_VU:
        return <DanhMucNghiepVu />;
      case BO_MA_MENU.BAO_HIEM_TAI_SAN:
        return <BaoHiemTaiSan />;
      case BO_MA_MENU.NHOM_DOI_TUONG:
        return <NhomDoiTuong />;
      case BO_MA_MENU.CAU_HINH_NGAN_SACH_HO_TRO:
        return <CauHinhNganSachHoTro />;
      case BO_MA_MENU.DANH_SACH_DON_VI_BHXH:
        return <DanhSachDonViBHXH />;
      case BO_MA_MENU.CAU_HINH_HUONG_HOA_HONG:
        return <CauHinhHuongHoaHong />;
      case BO_MA_MENU.TAI_KHOAN_DAI_LY:
        return <TaiKhoanDaiLy />;
      case BO_MA_MENU.XAY_DUNG_GOI_BAO_HIEM_DU_LICH:
        return <XayDungGoiBaoHiemDuLich />;
      default:
        return <TrangChu />;
    }
  }, []);

  const pathServer: PathProps[] = useMemo(() => {
    let arrPathServer: PathProps[] = [];
    arrPathServer = menuNguoiDung.map(itemMenu => {
      // Đảm bảo URL luôn bắt đầu bằng /
      let url = itemMenu.url;
      if (url && !url.startsWith("/")) {
        url = "/" + url;
      }
      return {
        path: url,
        isProtected: true,
        element: getScreenByMaMenu(itemMenu.ma || ""),
      };
    });

    // PUSH SCREEN VỀ AUTH VÀOs
    arrPathServer.push({
      path: ROUTER_PATHS.DANG_NHAP,
      element: <DangNhap />, // element: là component JSX sẽ được render khi route này khớp
      children: [], //children: là các route con (nested route)
      isProtected: false,
    });

    // Route khớp tất cả các path còn lại
    arrPathServer.push({
      path: "*",
      element: !profile.token ? <DangNhap /> : <NotFoundPage />, // element: là component JSX sẽ được render khi route này khớp
      children: [], //children: là các route con (nested route)
      isProtected: false,
    });
    // Route khớp đúng "/"
    arrPathServer.push({
      path: "/",
      element: <Dashboard />, // element: là component JSX sẽ được render khi route này khớp
      children: [], //children: là các route con (nested route)
      isProtected: true,
    });
    return arrPathServer;
  }, [menuNguoiDung, getScreenByMaMenu, profile]);

  const initData = useCallback(async () => {
    // Detect nhóm từ URL hiện tại
    const currentPath = window.location.pathname;
    // const nhom = currentPath.startsWith("/admin") ? "ADMIN" : "CLIENT";
    //lấy nhóm từ menu hiện tại đang đứng thì làm như thế nào?
    console.log("curentMenu", currentPath);
    console.log("menu người dùng", menuNguoiDung);

    const currentMenu = menuNguoiDung.find(menu => {
      if (!menu.url) return false;
      const menuPath = menu.url.startsWith("/") ? menu.url : "/" + menu.url;
      const menuNhom = menu.nhom;
      return menuNhom;
    });
    const nhom = currentMenu?.nhom;
    const response = await getListhMenuNguoiDungTheoNhom({nhom} as any);
    if (!response) {
      console.log("❌ getListhMenuNguoiDungTheoNhom trả về false");
      return;
    }
    getListDoiTac();
    getListChiNhanh();
    getDanhMucPhongBan();
    getDanhMucChucDanh();
    getDanhMucTinhThanh();
  }, []);

  useEffect(() => {
    //mở web sẽ load dữ liệu danh mục
    if (profile.token) {
      initData();
    } else {
      console.log("❌ Không có token");
    }
  }, [profile.token, initData]); // Thêm dependencies để useEffect chạy lại khi token thay đổi

  // NẾU CÓ SỰ THAY ĐỔI VỀ ACCESS_TOKEN THÌ SẼ GENERATE LẠI ROUTER
  const routes = useMemo(() => {
    const generatedRoutes = generateRouter(pathServer, !!profile.token);
    return generatedRoutes;
  }, [profile.token, pathServer]);

  // lưu thông tin user vào cookie khi thông tin user thay đổi
  useEffect(() => {
    setInfoToCookies(profile);
  }, [profile]);

  // change lang when state changes
  useEffect(() => {
    i18n.changeLanguage(lang);
  }, [lang]);

  // change theme mode when state changes
  useEffect(() => {
    toggleDarkMode(themeMode === ThemeMode.dark);
  }, [themeMode]);

  return (
    <Main>
      <NotiProvider>
        <MessageProvider>
          <Routes>{routes}</Routes>
        </MessageProvider>
      </NotiProvider>
    </Main>
  );
};
