import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {formatDateTimeToNumber} from "@src/utils";
import {Col, Flex, Form, message, Modal, Row} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useBaoHiemXeCoGioiContext} from "../../index.context";
import {ChiTietLichSuBoiThuongProps, FormChiTietLichSuBoiThuong, IModalChiTietLichSuBoiThuongRef} from "./Constant";

const {so_hs, ma_sp, lh_nv, tien_bt, tien_bt_vat, tong_tien_bt, ngay_mo_hs, ngay_duyet_bt, tien_dp, ma_doi_tac_ql} = FormChiTietLichSuBoiThuong;

const ModalChiTietLichSuBoiThuongComponent = forwardRef<IModalChiTietLichSuBoiThuongRef, ChiTietLichSuBoiThuongProps>(
  ({chiTietHopDong, listNguoiPhuThuoc, initListNguoiPhuThuoc}: ChiTietLichSuBoiThuongProps, ref) => {
    useImperativeHandle(ref, () => ({
      open: (dataLichSuBoiThuong?: any) => {
        setIsOpen(true);
        console.log("dataLichSuBoiThuong", dataLichSuBoiThuong);
        if (dataLichSuBoiThuong) setChiTietLichSuBoiThuong(dataLichSuBoiThuong); // nếu có dữ liệu -> set chi tiết đối tác -> là sửa
      },
      close: () => setIsOpen(false),
    }));
    const [chiTietLichSuBoiThuong, setChiTietLichSuBoiThuong] = useState<CommonExecute.Execute.ILichSuBoiThuongHopDong | null>(null);
    const [isOpen, setIsOpen] = useState(false);

    const {
      chiTietDoiTuongBaoHiemXe,
      //   loadingQuyenLoi,

      capNhatLichSuBoiThuong,
      getListSanPham,
      //   lietKeDieuKhoanNguoiDuocBaoHiem,
      layDanhSachLichSuBoiThuongHopDong,
      listSanPham,
      listDoiTac,
    } = useBaoHiemXeCoGioiContext();
    const [listQuyenLoiBaoHiem, setListQuyenLoiBaoHiem] = useState<CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi["gcn_dk"]>([]);
    const [formChiTietLichSuBoiThuong] = Form.useForm();
    const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
    const formValues = Form.useWatch([], formChiTietLichSuBoiThuong);
    // init form data - load dữ liệu vào form khi sửa
    useEffect(() => {
      if (chiTietLichSuBoiThuong) {
        const arrFormData = [];
        for (const key in chiTietLichSuBoiThuong) {
          let value: any = chiTietLichSuBoiThuong[key as keyof CommonExecute.Execute.ILichSuBoiThuongHopDong];
          if (key === "ngay_mo_hs" || key === "ngay_duyet_bt") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
          arrFormData.push({
            name: key,
            value,
          });
        }
        formChiTietLichSuBoiThuong.setFields(arrFormData);
        get
      }
      setListQuyenLoiBaoHiem(chiTietDoiTuongBaoHiemXe?.gcn_dk.map(item => ({...item, ma: item.lh_nv, ten: item.ten})) || []);
    }, [chiTietLichSuBoiThuong, chiTietDoiTuongBaoHiemXe?.gcn_dk]);
    // useEffect(() => {
    //   console.log(" chiTietDoiTuongBaoHiemXe", chiTietDoiTuongBaoHiemXe);
    //   if (chiTietDoiTuongBaoHiemXe?.gcn?.so_id && chiTietDoiTuongBaoHiemXe?.gcn?.so_id_dt) {
    //     lietKeDieuKhoanNguoiDuocBaoHiem({
    //       so_id: chiTietDoiTuongBaoHiemXe?.gcn?.so_id,
    //       so_id_dt: +(chiTietDoiTuongBaoHiemXe?.gcn?.so_id_dt || 0),
    //     });
    //   }
    // }, [chiTietDoiTuongBaoHiemXe]);
    // Tự động tính tổng tiền bồi thường khi tien_bt hoặc tien_bt_vat thay đổi
    useEffect(() => {
      const tienBt = formValues?.tien_bt || 0;
      const tienBtVat = formValues?.tien_bt_vat || 0;
      const tongTienBt = tienBt + tienBtVat;

      // Chỉ cập nhật nếu giá trị khác với giá trị hiện tại để tránh vòng lặp vô hạn
      if (formValues?.tong_tien_bt !== tongTienBt) {
        formChiTietLichSuBoiThuong.setFieldValue("tong_tien_bt", tongTienBt);
      }
    }, [formValues?.tien_bt, formValues?.tien_bt_vat, formValues?.tong_tien_bt, formChiTietLichSuBoiThuong]);

    //xử lý validate form
    useEffect(() => {
      formChiTietLichSuBoiThuong
        .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
        .then(() => {
          setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
        })
        .catch(() => {
          setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
        });
    }, [formChiTietLichSuBoiThuong, formValues]);

    const closeModal = useCallback(() => {
      setIsOpen(false);
      setChiTietLichSuBoiThuong(null);
      formChiTietLichSuBoiThuong.resetFields();
    }, [formChiTietLichSuBoiThuong]);

    // Change đối tác
    const onChangeDoiTac = (value: any) => {
      getListSanPham({ma_doi_tac_ql: value, nv: "XE"});
      //nếu doi tac thay doi -> reset ma_sp
      formChiTietLichSuBoiThuong.setFieldValue("ma_sp", undefined);
    };
    //Bấm Update
    const onClickLuuLichSuBoiThuong = async () => {
      try {
        console.log("formValues", formValues);
        const values: ReactQuery.ICapNhatLichSuBoiThuongHopDongParams = formChiTietLichSuBoiThuong.getFieldsValue(); //lấy ra values của form

        values.so_id = Number(chiTietDoiTuongBaoHiemXe?.gcn.so_id);
        values.so_id_dt = Number(chiTietDoiTuongBaoHiemXe?.gcn.so_id_dt);
        values.nv = "XE";
        values.so_hs = values.so_hs || "";

        values.stt = chiTietLichSuBoiThuong?.stt || (listNguoiPhuThuoc?.length || 0) + 1;

        // Debug: kiểm tra giá trị trước khi chuyển đổi
        console.log("Before conversion:", {
          tien_bt: values.tien_bt,
          tien_bt_vat: values.tien_bt_vat,
          tong_tien_bt: values.tong_tien_bt,
        });

        // Chuyển thành số với giá trị mặc định là 0 nếu undefined/null/NaN
        // Loại bỏ dấu phẩy trước khi chuyển đổi thành số
        const parseNumberFromString = (value: any): number => {
          if (value === null || value === undefined) return 0;
          if (typeof value === "number") return value;
          if (typeof value === "string") {
            // Loại bỏ dấu phẩy và chuyển thành số
            const cleanValue = value.replace(/,/g, "");
            const numValue = Number(cleanValue);
            return isNaN(numValue) ? 0 : numValue;
          }
          return 0;
        };

        values.tien_bt = parseNumberFromString(values.tien_bt);
        values.tien_bt_vat = parseNumberFromString(values.tien_bt_vat);
        values.tien_dp = parseNumberFromString(values.tien_dp);
        values.tong_tien_bt = values.tien_bt + values.tien_bt_vat;

        console.log("After conversion:", {
          tien_bt: values.tien_bt,
          tien_bt_vat: values.tien_bt_vat,
          tong_tien_bt: values.tong_tien_bt,
        });
        values.ngay_duyet_bt = formatDateTimeToNumber(values.ngay_duyet_bt);
        values.ngay_mo_hs = formatDateTimeToNumber(values.ngay_mo_hs);

        const response = await capNhatLichSuBoiThuong(values); //cập nhật lại đối tác
        if (response) {
          message.success(`${chiTietLichSuBoiThuong ? "Cập nhật" : "Thêm mới"} thành công`);
          layDanhSachLichSuBoiThuongHopDong({so_id: chiTietDoiTuongBaoHiemXe?.gcn.so_id, so_id_dt: chiTietDoiTuongBaoHiemXe?.gcn.so_id_dt, nv: "XE"});
          closeModal();
        }
      } catch (error) {
        console.log("onConfirm", error);
      }
    };

    // RENDER
    //FOOTER
    const renderFooter = () => {
      return (
        <div>
          <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
            Quay lại
          </Button>
          <Button disabled={disableSubmit} htmlType="submit" onClick={onClickLuuLichSuBoiThuong} icon={<CheckOutlined />}>
            Lưu
          </Button>
        </div>
      );
    };
    const renderFormColum = (props: IFormInput, span = 6) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
    const renderForm = () => (
      <Form form={formChiTietLichSuBoiThuong} layout="vertical">
        {/* MÃ */}
        <Row gutter={16}>
          {renderFormColum({...so_hs, disabled: chiTietLichSuBoiThuong ? true : false})}
          {renderFormColum({...ma_doi_tac_ql, options: listDoiTac, onChange: onChangeDoiTac, disabled: chiTietLichSuBoiThuong ? true : false})}
          {renderFormColum({...ma_sp, options: listSanPham})}
          {renderFormColum({...lh_nv, options: listQuyenLoiBaoHiem, disabled: chiTietLichSuBoiThuong ? true : false})}
          {renderFormColum({...ngay_mo_hs})}
          {renderFormColum({...tien_dp})}
          {renderFormColum({...tien_bt})}
          {renderFormColum({...tien_bt_vat})}
          {renderFormColum({...tong_tien_bt})}
          {renderFormColum({...ngay_duyet_bt})}
        </Row>
      </Form>
    );
    //Render
    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          title={
            <HeaderModal
              title={chiTietLichSuBoiThuong ? `${chiTietLichSuBoiThuong.so_hs}` : "Tạo mới lịch sử bồi thường"}
              // trang_thai_ten={chiTietNguoiPhuThuoc?.trang_thai_ten}
              // trang_thai={chiTietNguoiPhuThuoc?.trang_thai}
            />
          }
          destroyOnClose
          // centered
          maskClosable={false}
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={"70%"}
          styles={{
            body: {
              //   height: "20vh",
              // overflowY: "auto",
              // overflowX: "hidden",
            },
          }}
          footer={renderFooter}>
          {renderForm()}
        </Modal>
      </Flex>
    );
  },
);

ModalChiTietLichSuBoiThuongComponent.displayName = "ModalChiTietNguoiPhuThuocComponent";
export const ModalChiTietLichSuBoiThuong = memo(ModalChiTietLichSuBoiThuongComponent, isEqual);
