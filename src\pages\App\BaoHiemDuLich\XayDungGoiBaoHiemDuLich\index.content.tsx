import {PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Form, InputRef, Row, Table, TableColumnType, Tag} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {ModalThemGoiBaoHiem} from "./Component";
import {IModalThemGoiBaoHiemRef} from "./Component/Constant";
import {
  FormTimKiemGoiBaoHiem,
  listTrangThaiGoiBaoHiemSelect,
  radioItemTrangThaiGoiBaoHiemTable,
  tableGoiBaoHiemColumn,
  TableGoiBaoHiemColumnDataIndex,
  TableGoiBaoHiemColumnDataType,
} from "./index.configs";
import {useXayDungGoiBaoHiemContext} from "./index.context";
import "./index.default.scss";

const {ma_doi_tac_ql, ma_sp, ma, ten, nd_tim, ngay_ad, trang_thai} = FormTimKiemGoiBaoHiem;

const XayDungGoiBaoHiemContent: React.FC = memo(() => {
  const {listDoiTac, loading, listGoiBaoHiem, tongSoDongGoiBaoHiem, filterGoiBaoHiemParams, getChiTietGoiBaoHiem, setFilterGoiBaoHiemParams} = useXayDungGoiBaoHiemContext();

  const refModalThemGoiBaoHiem = useRef<IModalThemGoiBaoHiemRef>(null);

  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState<TableGoiBaoHiemColumnDataIndex | "">(""); //key column đang được search

  const [formTimKiemGoiBaoHiem] = Form.useForm();

  const dataTableListGoiBaoHiem = useMemo<Array<TableGoiBaoHiemColumnDataType>>(() => {
    try {
      const tableData = listGoiBaoHiem.map(itemGoiBaoHiem => {
        return {
          key: itemGoiBaoHiem.id,
          sott: itemGoiBaoHiem.sott,
          ma: itemGoiBaoHiem.ma,
          ten: itemGoiBaoHiem.ten,
          ten_doi_tac_ql: itemGoiBaoHiem.ten_doi_tac_ql,
          ngay_ad: itemGoiBaoHiem.ngay_ad,
          ten_sp: itemGoiBaoHiem.ten_sp,
          gioi_tinh: itemGoiBaoHiem.gioi_tinh,
          do_tuoi: itemGoiBaoHiem.do_tuoi,
          ngay_tao: itemGoiBaoHiem.ngay_tao,
          nguoi_tao: itemGoiBaoHiem.nguoi_tao,
          ngay_cap_nhat: itemGoiBaoHiem.ngay_cap_nhat,
          nguoi_cap_nhat: itemGoiBaoHiem.nguoi_cap_nhat,
          trang_thai_ten: itemGoiBaoHiem.trang_thai_ten,
          id: itemGoiBaoHiem.id,
        };
      });
      const arrEmptyRow: Array<TableGoiBaoHiemColumnDataType> = fillRowTableEmpty(tableData.length);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListGoiBaoHiem error", error);
      return [];
    }
  }, [listGoiBaoHiem]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableGoiBaoHiemColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableGoiBaoHiemColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams) => {
    setFilterGoiBaoHiemParams({...filterGoiBaoHiemParams, ...values, trang: 1, so_dong: 20});
  };

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 6) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderHeaderTableGoiBaoHiemConNguoi = () => {
    return (
      <Form
        form={formTimKiemGoiBaoHiem}
        // initialValues={{so_hd: "", ten_ndbh: "", den_ngay: dayjs(), tu_ngay: dayjs().subtract(1, "M")}}
        layout="vertical" //Tất cả các input nằm trên 1 dòng
        onFinish={onSearchApi}
        className="[&_.ant-form-item]:mb-0" // cho margin-bottom của form = 0
      >
        <Row gutter={16} align="bottom">
          {renderFormInputColum({
            ...ma_doi_tac_ql,
            options: listDoiTac,
            onChange: () => formTimKiemGoiBaoHiem.setFieldValue("ma_chi_nhanh_ql", undefined),
          })}
          {renderFormInputColum(ma_sp)}
          {renderFormInputColum(ma)}
          {renderFormInputColum(ten)}
        </Row>
        <Row gutter={16} align="bottom">
          {renderFormInputColum(nd_tim)}
          {renderFormInputColum(ngay_ad)}
          {renderFormInputColum({...trang_thai, options: listTrangThaiGoiBaoHiemSelect})}
          <Col span={3}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
          <Col span={3}>
            <Button type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalThemGoiBaoHiem.current?.open()} block>
              Tạo gói
            </Button>
          </Col>
        </Row>
      </Form>
    );
  };

  const getColumnSearchProps = (dataIndex: TableGoiBaoHiemColumnDataIndex, title: string): TableColumnType<TableGoiBaoHiemColumnDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? filterDropdownParams => (
            <TableFilterDropdown
              ref={refSearchInputTable}
              title={title}
              dataIndex={dataIndex}
              handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
              handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
              {...filterDropdownParams}
            />
          )
        : undefined,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} size={40} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ?.toString()
        .toLowerCase()
        .includes((value as string).toLowerCase()) || false,

    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
        }
      },
    },
    filterSearch: true,
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiGoiBaoHiemTable : undefined,
    render: (
      text,
      record,
      //  index
    ) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      ); // xử lý chuyển text thành 1 dòng khi text quá dài;
    },
  });

  return (
    <div id={ID_PAGE.XAY_DUNG_GOI_BAO_HIEM}>
      <Table<TableGoiBaoHiemColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListGoiBaoHiem} //mảng dữ liệu record được hiển thị
        //định nghĩa cột của table
        columns={
          tableGoiBaoHiemColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {
              ...item,
              ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableGoiBaoHiemColumnDataType, item.title as string)),
            };
          }) || []
        }
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDongGoiBaoHiem,
          onChange: (page, pageSize) => {
            //được gọi khi page size change
            setFilterGoiBaoHiemParams({...filterGoiBaoHiemParams, trang: page, so_dong: pageSize});
          },
        }}
        title={renderHeaderTableGoiBaoHiemConNguoi}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async () => {
              if (record.key.toString().includes("empty")) return;
              const response = await getChiTietGoiBaoHiem(record as ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams);
              if (response?.goi_bh) refModalThemGoiBaoHiem.current?.open(response);
            },
          };
        }}
      />
      <ModalThemGoiBaoHiem ref={refModalThemGoiBaoHiem} />
    </div>
  );
}, isEqual);

XayDungGoiBaoHiemContent.displayName = "XayDungGoiBaoHiemContent";

export default XayDungGoiBaoHiemContent;
